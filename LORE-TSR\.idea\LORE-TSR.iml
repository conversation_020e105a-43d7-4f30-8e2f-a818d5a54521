<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/src/bak" />
      <excludeFolder url="file://$MODULE_DIR$/src/lib/models/networks/bak" />
    </content>
    <orderEntry type="jdk" jdkName="Remote Python 3.10.13 (sftp://root@180.184.62.9:1322/opt/miniforge3/envs/lore/bin/python3)" jdkType="Python SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
  <component name="PyDocumentationSettings">
    <option name="format" value="PLAIN" />
    <option name="myDocStringFormat" value="Plain" />
  </component>
</module>