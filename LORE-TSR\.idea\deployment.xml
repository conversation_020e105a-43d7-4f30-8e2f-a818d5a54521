<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PublishConfigData" serverName="root@************:1322 agent (2)" remoteFilesAllowedToDisappearOnAutoupload="false">
    <serverData>
      <paths name="root@180.184.62.100:2022 agent">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="root@************:1222 agent">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="root@************:1222 agent (2)">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="root@************:1322 agent">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="root@************:1322 agent (2)">
        <serverdata>
          <mappings>
            <mapping deploy="/aipdf-mlp/lanx/workspace/projects/LORE" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
    </serverData>
  </component>
</project>