# LORE-TSR 到 train-anything 项目迁移需求文档 (PRD)

## 📋 项目概述

### 项目背景
将LORE-TSR项目的训练代码完整迁移到train-anything项目中，在table_structure_recognition部分新增lore_tsr相关组件，实现基于accelerate框架的统一训练平台。

### 核心原则
- **换壳不换芯**：保持LORE-TSR原有算法逻辑完全不变，仅适配新框架
- **直接复制而不是重构**：核心算法组件逐行复制，严禁重构
- **增量式迁移**：确保对train-anything现有功能完全兼容
- **结果可复现性**：迁移后模型效果与原LORE-TSR完全一致

## 🎯 迁移目标

### 功能目标
1. 在train-anything项目中新增完整的lore_tsr训练功能
2. 支持LORE-TSR的所有backbone架构选项
3. 保持原有的数据预处理pipeline物理含义
4. 实现与原LORE-TSR相同的损失函数计算
5. 支持原LORE-TSR预训练权重的加载和恢复训练

### 技术目标
1. 基于accelerate框架封装训练流程
2. 使用YAML配置文件+OmegaConf管理参数
3. 适配train-anything的分布式JSON数据格式
4. 集成ModelScope兼容的可视化功能
5. 维护完整的文件迁移映射表

## 📁 目标目录结构

### 新增目录组织
```
train-anything/
├── cmd_scripts/train_table_structure/
│   └── lore_tsr_train.sh                    # LORE-TSR训练脚本
├── training_loops/table_structure_recognition/
│   └── train_lore_tsr.py                    # LORE-TSR训练入口
├── networks/lore_tsr/                       # LORE-TSR网络架构
│   ├── __init__.py
│   ├── lore_tsr_model.py                    # 主模型工厂
│   ├── lore_tsr_loss.py                     # 损失函数
│   ├── processor.py                         # Processor组件
│   ├── transformer.py                       # Transformer实现
│   ├── backbones/                           # 骨干网络
│   │   ├── fpn_resnet.py
│   │   ├── fpn_resnet_half.py
│   │   ├── fpn_mask_resnet.py
│   │   ├── fpn_mask_resnet_half.py
│   │   └── pose_dla_dcn.py
│   └── heads/                               # 检测头
│       └── lore_tsr_head.py
├── my_datasets/table_structure_recognition/
│   ├── lore_tsr_dataset.py                  # LORE-TSR数据集适配器
│   ├── lore_tsr_transforms.py               # LORE-TSR数据变换
│   └── lore_tsr_target_preparation.py       # 目标准备逻辑
├── configs/table_structure_recognition/lore_tsr/
│   └── lore_tsr_config.yaml                 # LORE-TSR配置文件
├── modules/utils/lore_tsr/                  # LORE-TSR工具函数
│   ├── post_process.py
│   ├── oracle_utils.py
│   └── eval_utils.py
├── modules/visualization/
│   └── lore_tsr_visualizer.py               # LORE-TSR可视化器
└── external/lore_tsr/                       # 外部依赖
    ├── DCNv2/
    └── NMS/
```

## 🔄 迁移策略

### 三分法迁移原则

#### 1. 复制并保留核心算法 (Copy & Preserve Core Logic)
**对象**：LORE-TSR项目中所有实现核心算法的文件
- 模型定义 (`lib/models/`)
- 损失函数 (`lib/models/losses.py`)
- 后处理逻辑 (`lib/utils/post_process.py`)
- Transformer组件 (`lib/models/transformer.py`)
- Processor组件 (`lib/models/classifier.py`)

**原则**：近乎逐字地复制到train-anything的新目录中，只进行最小化的必要修改（如调整import路径），严禁重构或改变内部计算逻辑。

#### 2. 重构并适配框架入口 (Refactor & Adapt Framework Entrypoints)
**对象**：LORE-TSR项目中负责驱动流程的"胶水代码"
- 主入口 (`main.py`)
- 配置解析 (`opts.py`)
- 数据集加载与构建 (`dataset_factory.py`, `table.py`)
- 训练器/检测器 (`ctdet.py`, `base_detector.py`)

**原则**：不直接复制，以cycle-centernet-ms的最佳实践为模板，完全重构以深度集成train-anything的accelerate训练循环、OmegaConf配置系统和标准化数据集接口。

#### 3. 复制并隔离编译依赖 (Copy & Isolate Compiled Dependencies)
**对象**：需要手动编译的第三方库
- DCNv2 (可变形卷积)
- NMS (非极大值抑制)
- cocoapi

**原则**：将源代码原封不动地复制到`external/lore_tsr/`目录下，保持独立性。

## 📊 文件迁移映射策略

### 分层映射管理 (方案A)

#### 核心映射表 (优先级1)
直接影响训练流程的关键文件，约20-30个文件：

| 源文件路径 | 目标路径 | 迁移策略 | 说明 |
|-----------|---------|---------|------|
| `lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 复制保留 | 模型工厂函数 |
| `lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留 | 损失函数定义 |
| `lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留 | Processor组件 |
| `lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留 | Transformer实现 |
| `lib/models/networks/fpn_resnet_half.py` | `networks/lore_tsr/backbones/fpn_resnet_half.py` | 复制保留 | 骨干网络 |
| `main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配 | 训练入口 |
| `lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配 | 配置管理 |

#### 扩展映射表 (优先级2)
工具函数、可视化等支持性文件：

| 源文件路径 | 目标路径 | 迁移策略 | 说明 |
|-----------|---------|---------|------|
| `lib/utils/post_process.py` | `modules/utils/lore_tsr/post_process.py` | 复制保留 | 后处理逻辑 |
| `lib/utils/oracle_utils.py` | `modules/utils/lore_tsr/oracle_utils.py` | 复制保留 | Oracle工具 |
| `demo.py` | `modules/visualization/lore_tsr_visualizer.py` | 重构适配 | 可视化功能 |
| `lib/datasets/dataset/table_mid.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | 重构适配 | 数据集适配 |

#### 依赖映射表 (优先级3)
外部依赖和编译组件：

| 源文件路径 | 目标路径 | 迁移策略 | 说明 |
|-----------|---------|---------|------|
| `lib/models/networks/DCNv2/` | `external/lore_tsr/DCNv2/` | 复制隔离 | 可变形卷积 |
| `lib/external/` | `external/lore_tsr/NMS/` | 复制隔离 | NMS算法 |
| `cocoapi/` | `external/lore_tsr/cocoapi/` | 复制隔离 | COCO API |

### 映射管理原则
1. **完整性**：覆盖所有与训练流程相关的文件
2. **可追溯性**：每个迁移文件都有明确的源文件对应关系
3. **状态跟踪**：维护迁移状态（待迁移/进行中/已完成/已验证）
4. **依赖关系**：记录文件间的依赖关系，确保迁移顺序正确

## 🔧 技术实现规格

### 数据格式适配
#### 源格式 (COCO)
```json
{
  "logic_axis": [[row_start, row_end, col_start, col_end]],
  "segmentation": [[x1, y1, x2, y2, x3, y3, x4, y4]],
  "bbox": [x, y, width, height]
}
```

#### 目标格式 (分布式JSON)
```json
{
  "lloc": {
    "start_row": 0, "end_row": 0,
    "start_col": 0, "end_col": 0
  },
  "bbox": {
    "p1": [x1, y1], "p2": [x2, y2],
    "p3": [x3, y3], "p4": [x4, y4]
  }
}
```

#### 适配策略
1. **继承扩展**：继承现有TableDataset，添加LORE-TSR特有逻辑
2. **数值一致性**：确保lloc与logic_axis的数值完全对应
3. **几何转换**：将4点bbox转换为LORE-TSR所需的中心点+回归目标格式
4. **复用逻辑**：大部分数据处理逻辑复用Cycle-CenterNet-MS实现

### 模型架构迁移
#### 支持的Backbone选项
- `fpn_resnet.py` - ResNet+FPN标准版
- `fpn_resnet_half.py` - ResNet+FPN半尺寸版
- `fpn_mask_resnet.py` - 带掩码的ResNet+FPN
- `fpn_mask_resnet_half.py` - 带掩码的半尺寸版
- `pose_dla_dcn.py` - DLA+DCN架构

#### Processor组件集成
- **作用方式**：与原LORE-TSR项目完全相同
- **组织位置**：`networks/lore_tsr/processor.py`
- **依赖组件**：Transformer、位置嵌入、Stacker（可选）
- **集成方式**：在训练循环中作为模型后处理组件

### 配置项迁移规则
#### 迁移判断标准
- **平移**：LORE-TSR特有参数（如wiz_2dpe、wiz_stacking、tsfm_layers）
- **重命名**：新框架中有相同作用的参数
- **废弃**：被新框架直接接管的参数（如数据路径相关）

#### 冲突处理策略
- **报告机制**：遇到与accelerate框架冲突的配置，立即报告
- **扩展支持**：在新框架中扩展支持原有配置
- **参数保持**：保持与原LORE-TSR完全相同的参数名称和取值范围

### 损失函数完整性
#### 必须保留的损失组件
- `hm_loss` - 热力图损失 (FocalLoss)
- `wh_loss` - 边界框回归损失 (L1Loss)
- `off_loss` - 偏移回归损失 (RegL1Loss)
- `st_loss` - 结构损失 (PairLoss，可选)
- `ax_loss` - 轴向损失 (AxisLoss)
- `sax_loss` - 堆叠轴向损失 (AxisLoss，可选)

#### 权重配置保持
- `ax_loss` 固定权重2.0
- 支持配置覆盖
- 动态调整逻辑完全保持

### 权重兼容性方案
#### 加载策略
- **用户指定**：仅在用户指定权重路径时加载
- **转换工具**：提供独立的权重转换工具
- **格式适配**：适应新框架格式，借助新框架实现

#### 验证标准
- **输出一致性**：确保加载后的模型输出与原LORE-TSR完全一致
- **数值精度**：浮点数精度误差在可接受范围内

### 可视化功能扩展
#### 现有功能分析
**train-anything现有功能**：
- ModelScope兼容的推理流程
- 双通道热力图可视化
- 几何约束优化可视化
- 配置化样式管理

**LORE-TSR特有功能**：
- 基于COCO标注的可视化流程
- 批量图像处理
- 检测器完整推理流程

#### 需要扩展的功能
1. **逻辑坐标可视化**：lloc字段的可视化显示（必需功能）
2. **Processor输出可视化**：Transformer处理结果的可视化
3. **格式兼容**：兼容LORE-TSR检测器输出格式
4. **样式参照**：参照table_structure_visualizer_ms.py实现

## ✅ 验收标准

### 功能验收
1. **精度一致性**：在相同数据集上达到与原LORE-TSR相同的精度指标
2. **权重兼容性**：支持从LORE-TSR的checkpoint直接恢复到新框架继续训练
3. **配置完整性**：所有影响模型性能的配置项都已正确迁移
4. **可视化功能**：逻辑坐标可视化功能正常工作

### 兼容性验收
1. **增量兼容**：对train-anything现有功能完全兼容，无任何影响
2. **Cycle-CenterNet-MS兼容**：原有算法功能保持完全正常
3. **框架集成**：完全集成accelerate训练流程和OmegaConf配置系统

### 文档验收
1. **独立文档**：提供lore_tsr相关组件的独立文档和使用说明
2. **迁移记录**：完整的文件迁移映射表和状态跟踪
3. **配置说明**：详细的配置参数说明和示例

## 📝 交付物清单

### 代码交付物
1. **训练脚本**：`cmd_scripts/train_table_structure/lore_tsr_train.sh`
2. **训练入口**：`training_loops/table_structure_recognition/train_lore_tsr.py`
3. **网络架构**：`networks/lore_tsr/` 完整目录
4. **数据集适配**：`my_datasets/table_structure_recognition/lore_tsr_*` 系列文件
5. **配置文件**：`configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml`
6. **工具函数**：`modules/utils/lore_tsr/` 完整目录
7. **可视化器**：`modules/visualization/lore_tsr_visualizer.py`
8. **外部依赖**：`external/lore_tsr/` 完整目录

### 文档交付物
1. **迁移映射表**：完整的文件迁移状态跟踪表
2. **配置说明文档**：lore_tsr_config.yaml的详细说明
3. **使用指南**：LORE-TSR训练和推理的完整使用说明
4. **API文档**：新增组件的API接口文档

### 验证交付物
1. **权重转换工具**：LORE-TSR权重到新框架格式的转换脚本
2. **验证脚本**：模型输出一致性验证脚本
3. **测试用例**：完整的功能测试用例集

---

**文档版本**：v1.0  
**创建日期**：2025-07-18  
**最后更新**：2025-07-18
