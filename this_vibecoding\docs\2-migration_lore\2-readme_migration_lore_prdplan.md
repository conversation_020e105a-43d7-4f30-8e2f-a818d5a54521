# LORE-TSR 到 train-anything 迁移需求规划文档

## 📋 项目概述

### 迁移目标
将表格结构识别项目 LORE-TSR 从其独立的、基于自定义脚本的架构，完整迁移到 train-anything 框架中。迁移的核心是适配 train-anything 的现代化训练流程（基于 accelerate）、配置管理（基于 OmegaConf）和数据加载机制，同时保持 LORE-TSR 核心模型与损失函数的初始定义和交互逻辑不变，以确保算法效果的可复现性。

### 核心原则
- **换壳不换芯**：保持LORE-TSR原有算法逻辑完全不变，仅适配新框架
- **直接复制而不是重构**：核心算法组件逐行复制，严禁重构
- **增量式迁移**：确保对train-anything现有功能完全兼容
- **结果可复现性**：迁移后模型效果与原LORE-TSR完全一致

## 🔄 迁移策略

### 三分法迁移原则

#### 1. 复制并保留核心算法 (Copy & Preserve Core Logic)
**对象**：LORE-TSR项目中所有实现核心算法的文件
- 模型定义 (`lib/models/`)
- 损失函数 (`lib/models/losses.py`)
- 后处理逻辑 (`lib/utils/post_process.py`)
- Transformer组件 (`lib/models/transformer.py`)
- Processor组件 (`lib/models/classifier.py`)

**原则**：近乎逐字地复制到train-anything的新目录中，只进行最小化的必要修改（如调整import路径），严禁重构或改变内部计算逻辑。

#### 2. 重构并适配框架入口 (Refactor & Adapt Framework Entrypoints)
**对象**：LORE-TSR项目中负责驱动流程的"胶水代码"
- 主入口 (`main.py`)
- 配置解析 (`opts.py`)
- 数据集加载与构建 (`dataset_factory.py`, `table.py`)
- 训练器/检测器 (`ctdet.py`, `base_detector.py`)

**原则**：不直接复制，以cycle-centernet-ms的最佳实践为模板，完全重构以深度集成train-anything的accelerate训练循环、OmegaConf配置系统和标准化数据集接口。

#### 3. 复制并隔离编译依赖 (Copy & Isolate Compiled Dependencies)
**对象**：需要手动编译的第三方库
- DCNv2 (可变形卷积)
- NMS (非极大值抑制)
- cocoapi

**原则**：将源代码原封不动地复制到`external/lore_tsr/`目录下，保持独立性。

## 📅 迭代规划

### 第一阶段：基础设施迭代（MVP）

#### 迭代1：基础目录结构和配置系统
**优先级**：P0（必须）
**目标**：建立LORE-TSR在train-anything中的基础目录结构和配置框架
**交付物**：
- `networks/lore_tsr/` 完整目录结构
- `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` 基础配置文件
- `training_loops/table_structure_recognition/train_lore_tsr.py` 空框架文件
- 基础的import路径和模块初始化

**验证标准**：
- 配置文件能被OmegaConf正确解析
- 目录结构符合train-anything规范
- 模块导入无错误

**依赖关系**：无
**风险点**：配置系统复杂性，需要深入理解OmegaConf

#### 迭代2：核心模型架构迁移
**优先级**：P0（必须）
**目标**：迁移LORE-TSR的核心模型定义和骨干网络
**交付物**：
- `networks/lore_tsr/lore_tsr_model.py` - 模型工厂函数
- `networks/lore_tsr/backbones/fpn_resnet_half.py` - 主要骨干网络
- `networks/lore_tsr/heads/lore_tsr_head.py` - 检测头定义
- 模型创建和初始化逻辑

**验证标准**：
- 模型能够成功实例化
- 前向传播无错误
- 模型结构与原LORE-TSR一致

**依赖关系**：迭代1
**风险点**：模型结构复杂性，import路径调整

#### 迭代3：基础训练循环
**优先级**：P0（必须）
**目标**：实现基于accelerate的最小训练循环框架
**交付物**：
- 完整的`train_lore_tsr.py`训练入口
- 基础的数据加载逻辑（使用dummy数据）
- 简化的训练循环（无完整损失函数）
- accelerate集成和多GPU支持

**验证标准**：
- 训练循环能够运行
- 模型参数能够更新
- accelerate功能正常工作

**依赖关系**：迭代1,2
**风险点**：accelerate框架学习曲线，多GPU兼容性

### 第二阶段：核心功能迭代

#### 迭代4：损失函数完整迁移
**优先级**：P0（必须）
**目标**：完整迁移LORE-TSR的所有损失函数组件
**交付物**：
- `networks/lore_tsr/lore_tsr_loss.py` - 完整损失函数类
- 所有子损失组件（hm_loss, wh_loss, off_loss, st_loss, ax_loss, sax_loss）
- 损失权重配置支持
- 损失计算逻辑集成到训练循环

**验证标准**：
- 损失函数计算结果与原LORE-TSR完全一致
- 所有损失组件正常工作
- 权重配置生效

**依赖关系**：迭代1,2,3
**风险点**：损失函数复杂性，数值精度一致性

#### 迭代5：数据集适配器实现
**优先级**：P0（必须）
**目标**：实现LORE-TSR数据格式到train-anything格式的适配
**交付物**：
- `my_datasets/table_structure_recognition/lore_tsr_dataset.py` - 数据集类
- `my_datasets/table_structure_recognition/lore_tsr_transforms.py` - 数据变换
- `my_datasets/table_structure_recognition/lore_tsr_target_preparation.py` - 目标准备
- COCO格式到分布式JSON格式的转换逻辑

**验证标准**：
- 数据加载正常，无错误
- 数据格式转换正确
- 与原LORE-TSR数据处理逻辑一致

**依赖关系**：迭代1,3
**风险点**：数据格式转换精度，几何变换一致性

#### 迭代6：Processor组件集成
**优先级**：P0（必须）
**目标**：集成LORE-TSR的Processor和Transformer组件
**交付物**：
- `networks/lore_tsr/processor.py` - Processor组件
- `networks/lore_tsr/transformer.py` - Transformer实现
- 在训练循环中的集成逻辑
- 逻辑结构恢复功能

**验证标准**：
- Processor组件能够正确处理模型输出
- 逻辑结构恢复功能正常
- 与原LORE-TSR行为一致

**依赖关系**：迭代1,2,3,5
**风险点**：Transformer复杂性，序列处理逻辑

### 第三阶段：完整性迭代

#### 迭代7：外部依赖集成
**优先级**：P1（重要）
**目标**：集成LORE-TSR需要的外部编译依赖
**交付物**：
- `external/lore_tsr/DCNv2/` - 可变形卷积完整代码
- `external/lore_tsr/NMS/` - 非极大值抑制完整代码
- `external/lore_tsr/cocoapi/` - COCO API完整代码
- 编译脚本和安装说明文档

**验证标准**：
- 外部依赖能够正确编译
- 相关功能正常工作
- 编译过程文档化

**依赖关系**：迭代1,2
**风险点**：编译环境依赖，跨平台兼容性

#### 迭代8：权重兼容性实现
**优先级**：P1（重要）
**目标**：实现LORE-TSR预训练权重的加载和转换
**交付物**：
- 权重转换工具脚本
- 权重加载逻辑集成到训练循环
- 权重格式适配代码
- 权重验证工具

**验证标准**：
- 能够加载原LORE-TSR权重
- 模型输出与原版本一致
- 权重转换无精度损失

**依赖关系**：迭代1,2,3,7
**风险点**：权重格式兼容性，状态字典映射

#### 迭代9：可视化功能扩展
**优先级**：P2（可选）
**目标**：扩展train-anything的可视化功能以支持LORE-TSR
**交付物**：
- `modules/visualization/lore_tsr_visualizer.py` - 可视化器
- 逻辑坐标可视化功能
- Processor输出可视化
- ModelScope兼容的可视化接口

**验证标准**：
- 可视化功能正常工作
- 能够正确显示LORE-TSR的检测结果
- 与现有可视化系统兼容

**依赖关系**：迭代1,6
**风险点**：可视化复杂性，格式兼容性

### 第四阶段：验证和优化迭代

#### 迭代10：端到端验证
**优先级**：P0（必须）
**目标**：验证整个迁移的正确性和完整性
**交付物**：
- 端到端训练验证脚本
- 模型输出一致性验证工具
- 精度对比测试用例
- 性能基准测试

**验证标准**：
- 在相同数据集上达到与原LORE-TSR相同的精度指标
- 训练收敛性正常
- 无功能回归

**依赖关系**：迭代1,2,3,4,5,6,8
**风险点**：精度一致性验证复杂性，性能回归

#### 迭代11：性能优化和文档完善
**优先级**：P1（重要）
**目标**：优化性能并完善文档和工具
**交付物**：
- `cmd_scripts/train_table_structure/lore_tsr_train.sh` - 训练脚本
- `modules/utils/lore_tsr/` - 工具函数目录
- 完整的使用文档和API文档
- 文件迁移映射表
- 性能优化代码

**验证标准**：
- 文档完整且准确
- 性能满足要求
- 所有工具正常工作

**依赖关系**：迭代10
**风险点**：文档完整性，性能优化效果

## 📊 技术规格

### 目标目录结构
```
train-anything/
├── cmd_scripts/train_table_structure/
│   └── lore_tsr_train.sh
├── training_loops/table_structure_recognition/
│   └── train_lore_tsr.py
├── networks/lore_tsr/
│   ├── __init__.py
│   ├── lore_tsr_model.py
│   ├── lore_tsr_loss.py
│   ├── processor.py
│   ├── transformer.py
│   ├── backbones/
│   │   ├── fpn_resnet.py
│   │   ├── fpn_resnet_half.py
│   │   ├── fpn_mask_resnet.py
│   │   ├── fpn_mask_resnet_half.py
│   │   └── pose_dla_dcn.py
│   └── heads/
│       └── lore_tsr_head.py
├── my_datasets/table_structure_recognition/
│   ├── lore_tsr_dataset.py
│   ├── lore_tsr_transforms.py
│   └── lore_tsr_target_preparation.py
├── configs/table_structure_recognition/lore_tsr/
│   └── lore_tsr_config.yaml
├── modules/utils/lore_tsr/
│   ├── post_process.py
│   ├── oracle_utils.py
│   └── eval_utils.py
├── modules/visualization/
│   └── lore_tsr_visualizer.py
└── external/lore_tsr/
    ├── DCNv2/
    ├── NMS/
    └── cocoapi/
```

### 核心文件迁移映射

| 源文件路径 | 目标路径 | 迁移策略 | 迭代 |
|-----------|---------|---------|------|
| `lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 复制保留 | 2 |
| `lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留 | 4 |
| `lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留 | 6 |
| `lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留 | 6 |
| `lib/models/networks/fpn_resnet_half.py` | `networks/lore_tsr/backbones/fpn_resnet_half.py` | 复制保留 | 2 |
| `main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配 | 3 |
| `lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配 | 1 |
| `lib/utils/post_process.py` | `modules/utils/lore_tsr/post_process.py` | 复制保留 | 11 |
| `lib/datasets/dataset/table_mid.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | 重构适配 | 5 |
| `lib/models/networks/DCNv2/` | `external/lore_tsr/DCNv2/` | 复制隔离 | 7 |

### 数据格式适配规格

#### 源格式 (COCO)
```json
{
  "logic_axis": [[row_start, row_end, col_start, col_end]],
  "segmentation": [[x1, y1, x2, y2, x3, y3, x4, y4]],
  "bbox": [x, y, width, height]
}
```

#### 目标格式 (分布式JSON)
```json
{
  "lloc": {
    "start_row": 0, "end_row": 0,
    "start_col": 0, "end_col": 0
  },
  "bbox": {
    "p1": [x1, y1], "p2": [x2, y2],
    "p3": [x3, y3], "p4": [x4, y4]
  }
}
```

### 损失函数规格
必须保留的损失组件：
- `hm_loss` - 热力图损失 (FocalLoss)
- `wh_loss` - 边界框回归损失 (L1Loss)
- `off_loss` - 偏移回归损失 (RegL1Loss)
- `st_loss` - 结构损失 (PairLoss，可选)
- `ax_loss` - 轴向损失 (AxisLoss)，固定权重2.0
- `sax_loss` - 堆叠轴向损失 (AxisLoss，可选)

## ✅ 验收标准

### 功能验收
1. **精度一致性**：在相同数据集上达到与原LORE-TSR相同的精度指标
2. **权重兼容性**：支持从LORE-TSR的checkpoint直接恢复到新框架继续训练
3. **配置完整性**：所有影响模型性能的配置项都已正确迁移
4. **可视化功能**：逻辑坐标可视化功能正常工作

### 兼容性验收
1. **增量兼容**：对train-anything现有功能完全兼容，无任何影响
2. **Cycle-CenterNet-MS兼容**：原有算法功能保持完全正常
3. **框架集成**：完全集成accelerate训练流程和OmegaConf配置系统

### 技术验收
1. **代码质量**：代码结构清晰，符合train-anything规范
2. **性能要求**：训练性能不低于原LORE-TSR
3. **稳定性**：长时间训练稳定，无内存泄漏

## 🚨 风险管理

### 技术风险
1. **DCNv2等外部依赖编译问题**
   - 缓解措施：提供详细编译文档，支持多种编译环境
   - 应急方案：提供预编译版本

2. **权重格式兼容性问题**
   - 缓解措施：开发专门的权重转换工具
   - 应急方案：重新训练权重

3. **数据格式转换精度损失**
   - 缓解措施：严格的数值验证测试
   - 应急方案：保留原始数据处理逻辑

### 集成风险
1. **与train-anything现有功能冲突**
   - 缓解措施：充分的兼容性测试
   - 应急方案：隔离LORE-TSR相关功能

2. **accelerate框架学习曲线**
   - 缓解措施：参考cycle-centernet-ms最佳实践
   - 应急方案：简化accelerate使用

### 项目风险
1. **迭代依赖关系复杂**
   - 缓解措施：严格按照依赖关系执行
   - 应急方案：调整迭代顺序

2. **验证复杂性高**
   - 缓解措施：自动化验证工具
   - 应急方案：手动验证关键功能

## 📝 最终交付清单

### 代码交付物
1. **训练脚本**：`cmd_scripts/train_table_structure/lore_tsr_train.sh`
2. **训练入口**：`training_loops/table_structure_recognition/train_lore_tsr.py`
3. **网络架构**：`networks/lore_tsr/` 完整目录
4. **数据集适配**：`my_datasets/table_structure_recognition/lore_tsr_*` 系列文件
5. **配置文件**：`configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml`
6. **工具函数**：`modules/utils/lore_tsr/` 完整目录
7. **可视化器**：`modules/visualization/lore_tsr_visualizer.py`
8. **外部依赖**：`external/lore_tsr/` 完整目录

### 文档交付物
1. **迁移映射表**：完整的文件迁移状态跟踪表
2. **配置说明文档**：lore_tsr_config.yaml的详细说明
3. **使用指南**：LORE-TSR训练和推理的完整使用说明
4. **API文档**：新增组件的API接口文档

### 验证交付物
1. **权重转换工具**：LORE-TSR权重到新框架格式的转换脚本
2. **验证脚本**：模型输出一致性验证脚本
3. **测试用例**：完整的功能测试用例集

---

**文档版本**：v1.0  
**创建日期**：2025-07-18  
**最后更新**：2025-07-18  
**总迭代数**：11个  
**预估总工期**：22-33个工作日（按每个迭代2-3天计算）
