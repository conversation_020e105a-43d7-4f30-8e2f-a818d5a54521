# LORE-TSR 到 train-anything 迁移开发计划 - 步骤一.二

## 📋 项目概述

本文档基于步骤1.1的成功执行结果，制定LORE-TSR项目迁移到train-anything框架的**迭代1第2步**开发计划。继续遵循"小步快跑、持续验证"的开发模式，专注于配置系统的建立。

### 步骤1.1执行结果回顾
✅ **已完成**：基础目录结构和模块初始化
- 所有目录结构已创建完成
- 所有__init__.py文件已创建并能正常导入
- 基础架构完整，为后续迭代预留了扩展点

## 🗺️ 动态迁移蓝图 (更新版)

### 文件迁移映射表和逻辑图

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 当前迭代 | 复杂度 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配：转换为OmegaConf YAML格式 | 迭代1 | **复杂** | `进行中` |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配：适配accelerate框架 | 迭代1,3 | **复杂** | `未开始` |
| `N/A` | `modules/proj_cmd_args/lore_tsr/args.py` | 新建：命令行参数解析模块 | 迭代1 | **复杂** | `进行中` |
| `src/lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 复制保留：模型工厂函数 | 迭代2 | **复杂** | `未开始` |
| `src/lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留：逐行复制保持精度 | 迭代4 | 简单 | `未开始` |
| `src/lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留：Processor组件 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留：Transformer实现 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/networks/fpn_resnet_half.py` | `networks/lore_tsr/backbones/fpn_resnet_half.py` | 复制保留：主要骨干网络 | 迭代2 | 简单 | `未开始` |
| `src/lib/datasets/dataset/table_mid.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | 重构适配：数据集适配器 | 迭代5 | **复杂** | `未开始` |
| `src/lib/utils/post_process.py` | `modules/utils/lore_tsr/post_process.py` | 复制保留：后处理工具 | 迭代11 | 简单 | `未开始` |
| `src/lib/models/networks/DCNv2/` | `external/lore_tsr/DCNv2/` | 复制隔离：可变形卷积 | 迭代7 | 简单 | `未开始` |

### 目标目录结构树 (更新版)

```text
train-anything/
├── configs/table_structure_recognition/lore_tsr/  # [已创建]
│   └── lore_tsr_config.yaml                      # [本步骤创建]
├── training_loops/table_structure_recognition/   # [已创建]
│   └── train_lore_tsr.py                         # [待创建]
├── networks/lore_tsr/                            # [已创建]
│   ├── __init__.py                               # [已创建]
│   ├── lore_tsr_model.py                         # [待创建]
│   ├── lore_tsr_loss.py                          # [待创建]
│   ├── processor.py                              # [待创建]
│   ├── transformer.py                            # [待创建]
│   ├── backbones/                                # [已创建]
│   │   ├── __init__.py                           # [已创建]
│   │   └── (其他骨干网络文件)                      # [待创建]
│   └── heads/                                    # [已创建]
│       ├── __init__.py                           # [已创建]
│       └── lore_tsr_head.py                      # [待创建]
├── my_datasets/table_structure_recognition/      # [已创建]
│   └── (数据集相关文件)                           # [待创建]
├── modules/proj_cmd_args/lore_tsr/               # [已创建]
│   ├── __init__.py                               # [已创建]
│   └── args.py                                   # [本步骤创建]
├── modules/utils/lore_tsr/                       # [已创建]
│   ├── __init__.py                               # [已创建]
│   └── (工具函数文件)                             # [待创建]
└── external/lore_tsr/                            # [已创建]
    ├── __init__.py                               # [已创建]
    └── (外部依赖目录)                             # [待创建]
```

## 🎯 迭代1第2步：创建配置文件和参数解析

### 步骤标题
**迭代1步骤1.2: 创建配置文件和命令行参数解析模块**

### 当前迭代
**迭代1：基础目录结构和配置系统**

### 影响文件
本步骤将创建以下文件：
- `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` - 主配置文件
- `modules/proj_cmd_args/lore_tsr/args.py` - 命令行参数解析模块

### 具体操作

#### 1. 创建LORE-TSR配置文件
基于LORE-TSR的opts.py文件，将所有参数转换为OmegaConf YAML格式，参考cycle-centernet的配置文件结构。

**配置文件结构设计**：
```yaml
# 基础配置
basic:
  debug: false
  seed: 317
  output_dir: /tmp/lore_tsr_training_output
  only_vis_log: false

# 数据配置
data:
  paths:
    train_data_dir: ["/path/to/lore_tsr/train/data"]
    val_data_dir: ["/path/to/lore_tsr/val/data"]
  processing:
    max_samples: null
    image_size: [768, 768]  # LORE-TSR默认ctdet_mid尺寸
    normalize:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
      to_rgb: true
    down_ratio: 4
  loader:
    num_workers: 4
    pin_memory: true

# 模型配置
model:
  arch_name: "resfpnhalf_18"  # LORE-TSR默认架构
  pretrained: false
  head_conv: 64
  heads:
    hm: 2      # 热力图通道数
    wh: 8      # 边界框通道数
    reg: 2     # 偏移通道数
    st: 8      # 结构通道数
    ax: 256    # 轴向特征通道数
    cr: 256    # 角点特征通道数

# 处理器配置 (LORE-TSR特有)
processor:
  wiz_2dpe: false           # 启用2D位置嵌入
  wiz_stacking: false       # 启用堆叠回归器
  wiz_4ps: false           # 启用四角点特征
  tsfm_layers: 6           # Transformer层数
  stacking_layers: 3       # 堆叠层数
  hidden_size: 256         # 隐藏层大小
  K: 100                   # 最大检测数量
  MK: 700                  # 最大关键点数量

# 损失配置
loss:
  weights:
    hm_weight: 1.0         # 热力图损失权重
    wh_weight: 1.0         # 边界框损失权重
    off_weight: 1.0        # 偏移损失权重
    st_weight: 1.0         # 结构损失权重
    mk_weight: 1.0         # 角点热图损失权重

# 训练配置
training:
  epochs: 90
  batch_size: 32
  optimizer:
    type: "Adam"
    learning_rate: 1.25e-4
    adamx:
      beta1: 0.9
      beta2: 0.999
      epsilon: 1e-8
  scheduler:
    type: "step"
    step:
      milestones: [80]
      gamma: 0.1
```

#### 2. 创建命令行参数解析模块
基于train-anything框架的OmegaConf系统，参考cycle-centernet的args.py实现。

**args.py模块结构**：
```python
#!/usr/bin/env python3
"""
LORE-TSR 命令行参数解析模块
基于 train-anything 框架的 OmegaConf 配置系统
"""

import argparse
from pathlib import Path
from omegaconf import DictConfig

# 导入train-anything框架的配置解析器
from modules.utils.config_parser import load_config

def parse_args() -> DictConfig:
    """解析LORE-TSR训练的命令行参数和配置文件"""
    parser = argparse.ArgumentParser(
        description="LORE-TSR 表格结构识别训练",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # 基础参数
    parser.add_argument(
        "--config",
        type=str,
        default="configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml",
        help="配置文件路径"
    )

    parser.add_argument(
        "-o", "--override",
        action="append",
        default=[],
        help="覆盖配置参数，格式：key=value"
    )

    # 快速调试参数
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--dry-run", action="store_true", help="启用干运行模式")

    args = parser.parse_args()

    # 预处理覆盖参数
    overrides = _preprocess_override_args(args.override)

    # 添加命令行快速参数到覆盖列表
    if args.debug:
        overrides.append("basic.debug=true")
    if args.dry_run:
        overrides.append("debug_visualization.dry_run=true")

    # 加载配置文件并应用覆盖
    config = load_config(args.config, overrides)
    return config
```

### 受影响的现有模块
- **无影响**：本步骤只创建新文件，不修改train-anything现有模块
- **兼容性**：配置文件格式完全兼容OmegaConf系统
- **扩展性**：为后续迭代预留了配置空间

### 复用已有代码
- 参考 `configs/table_structure_recognition/cycle_centernet/cycle_centernet_ms_config.yaml` 的配置结构
- 复用 `modules/proj_cmd_args/cycle_centernet/args.py` 的参数解析逻辑
- 遵循 train-anything 框架的OmegaConf配置规范

### 如何验证 (Verification)

#### 验证命令1：配置文件解析测试
```bash
# 测试配置文件能被OmegaConf正确解析
python -c "
from omegaconf import OmegaConf
config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
print('✅ 配置文件解析成功')
print(f'模型架构: {config.model.arch_name}')
print(f'训练轮次: {config.training.epochs}')
print(f'批次大小: {config.training.batch_size}')
"
```

#### 验证命令2：参数解析模块测试
```bash
# 测试命令行参数解析模块
python -c "
import sys
sys.path.append('.')
from modules.proj_cmd_args.lore_tsr.args import parse_args
import sys
sys.argv = ['test', '--config', 'configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml']
config = parse_args()
print('✅ 参数解析模块工作正常')
print(f'配置类型: {type(config)}')
print(f'基础配置: {config.basic}')
"
```

#### 验证命令3：配置覆盖功能测试
```bash
# 测试配置参数覆盖功能
python -c "
import sys
sys.path.append('.')
from modules.proj_cmd_args.lore_tsr.args import parse_args
sys.argv = ['test', '--config', 'configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml', '-o', 'training.batch_size=16', '-o', 'basic.debug=true']
config = parse_args()
print('✅ 配置覆盖功能正常')
print(f'批次大小: {config.training.batch_size}')
print(f'调试模式: {config.basic.debug}')
"
```

### 当前迭代逻辑图

```mermaid
graph TD
    %% 当前迭代：迭代1步骤1.2 - 创建配置文件和参数解析

    subgraph "源：LORE-TSR/src/lib/opts.py"
        direction TB
        S1["命令行参数定义"]
        S2["配置参数分类"]
        S3["默认值设置"]
        S4["参数后处理逻辑"]
        
        S1 --> S2
        S2 --> S3
        S3 --> S4
    end

    subgraph "目标：train-anything配置系统"
        direction TB
        T1["lore_tsr_config.yaml"]
        T2["args.py"]
        T3["OmegaConf集成"]
        
        T1 --> T3
        T2 --> T3
    end

    subgraph "转换映射"
        direction LR
        M1["基础实验设置 → basic"]
        M2["系统配置 → data.loader"]
        M3["模型配置 → model"]
        M4["处理器配置 → processor"]
        M5["训练配置 → training"]
        M6["损失配置 → loss"]
    end

    subgraph "验证检查"
        direction LR
        V1["OmegaConf解析测试"]
        V2["参数解析模块测试"]
        V3["配置覆盖功能测试"]
    end

    %% 迁移映射
    S2 -- "重构适配" --> M1
    S2 -- "重构适配" --> M2
    S2 -- "重构适配" --> M3
    S2 -- "重构适配" --> M4
    S2 -- "重构适配" --> M5
    S2 -- "重构适配" --> M6

    M1 --> T1
    M2 --> T1
    M3 --> T1
    M4 --> T1
    M5 --> T1
    M6 --> T1

    S1 -- "重构适配" --> T2
    S4 -- "框架处理" --> T3

    %% 验证流程
    T1 --> V1
    T2 --> V2
    T3 --> V3
```

## 📝 步骤完成标准

### 功能验收
1. **配置文件完整**：所有LORE-TSR关键参数都已正确映射到YAML格式
2. **参数解析正常**：命令行参数解析模块能正确加载配置和处理覆盖
3. **OmegaConf兼容**：配置文件完全兼容train-anything的OmegaConf系统
4. **默认值正确**：所有参数的默认值与原LORE-TSR保持一致

### 技术验收
1. **代码质量**：配置文件格式规范，参数解析模块符合Python代码规范
2. **文档完整性**：配置文件有清晰的注释说明每个参数的用途
3. **扩展性验证**：配置系统为后续迭代预留了正确的扩展空间
4. **兼容性确认**：完全符合train-anything框架的配置管理规范

### 集成验收
1. **框架集成**：配置系统完全集成到train-anything的OmegaConf体系中
2. **参数映射**：所有LORE-TSR参数都有明确的映射关系和文档说明
3. **覆盖机制**：支持命令行参数覆盖配置文件中的任意参数

## 🚨 风险管理

### 技术风险
1. **参数映射错误**：确保所有LORE-TSR参数都正确映射，无遗漏
2. **默认值不一致**：验证所有默认值与原LORE-TSR完全一致
3. **配置格式问题**：确保YAML格式正确，无语法错误

### 缓解措施
1. **逐项对比**：逐一对比opts.py中的每个参数，确保无遗漏
2. **默认值验证**：对比原始默认值，确保一致性
3. **格式验证**：使用OmegaConf加载测试，确保格式正确

## 📊 LORE-TSR参数映射表

### 核心参数映射关系

| LORE-TSR参数 | train-anything配置路径 | 默认值 | 说明 |
|-------------|----------------------|-------|------|
| `task` | `basic.task` | "ctdet_mid" | 任务类型 |
| `dataset` | `basic.dataset` | "table" | 数据集类型 |
| `arch` | `model.arch_name` | "resfpnhalf_18" | 模型架构名称 |
| `heads` | `model.heads` | {hm:2, wh:8, reg:2, st:8, ax:256, cr:256} | 输出头配置 |
| `head_conv` | `model.head_conv` | 64 | 检测头中间层通道数 |
| `wiz_2dpe` | `processor.wiz_2dpe` | false | 启用2D位置嵌入 |
| `wiz_stacking` | `processor.wiz_stacking` | false | 启用堆叠回归器 |
| `wiz_4ps` | `processor.wiz_4ps` | false | 启用四角点特征 |
| `tsfm_layers` | `processor.tsfm_layers` | 6 | Transformer层数 |
| `stacking_layers` | `processor.stacking_layers` | 3 | 堆叠层数 |
| `hidden_size` | `processor.hidden_size` | 256 | 隐藏层大小 |
| `lr` | `training.optimizer.learning_rate` | 1.25e-4 | 学习率 |
| `lr_step` | `training.scheduler.step.milestones` | [80] | 学习率衰减节点 |
| `batch_size` | `training.batch_size` | 32 | 批次大小 |
| `num_epochs` | `training.epochs` | 90 | 训练轮次 |
| `num_workers` | `data.loader.num_workers` | 4 | 数据加载器工作进程数 |
| `seed` | `basic.seed` | 317 | 随机种子 |
| `K` | `processor.K` | 100 | 最大检测数量 |
| `MK` | `processor.MK` | 700 | 最大关键点数量 |
| `hm_weight` | `loss.weights.hm_weight` | 1.0 | 热力图损失权重 |
| `wh_weight` | `loss.weights.wh_weight` | 1.0 | 边界框损失权重 |
| `off_weight` | `loss.weights.off_weight` | 1.0 | 偏移损失权重 |
| `st_weight` | `loss.weights.st_weight` | 1.0 | 结构损失权重 |

### 配置文件完整模板

基于上述映射关系，完整的lore_tsr_config.yaml文件应包含以下所有配置项：

```yaml
# LORE-TSR 表格结构识别训练配置文件
# Time: 2025-07-18
# Author: Migration from LORE-TSR to train-anything
# Description: 基于OmegaConf的层级配置文件，适配LORE-TSR算法到train-anything框架

# ============================================================================
# 基础配置
# ============================================================================
basic:
  # 任务类型
  task: "ctdet_mid"
  # 数据集类型
  dataset: "table"
  # 数据集名称
  dataset_name: "WTW"
  # 实验ID
  exp_id: "default"
  # 调试模式级别 (0: 无调试, 1: 基础可视化, 2: 网络输出, 3: matplotlib, 4: 保存到磁盘)
  debug: 0
  # 随机种子
  seed: 317
  # 输出目录
  output_dir: /tmp/lore_tsr_training_output
  # 只执行可视化功能，不进行训练和验证
  only_vis_log: false
  # 是否为测试模式
  test: false

# ============================================================================
# 数据配置
# ============================================================================
data:
  # 数据路径配置
  paths:
    # 训练数据目录
    train_data_dir:
      - /path/to/lore_tsr/train/data
    # 验证数据目录
    val_data_dir:
      - /path/to/lore_tsr/val/data
    # 图像目录
    image_dir: "/aipdf-mlp/shared/tsr_dataset/WTW/train/images"
    # 注释文件路径
    anno_path: ""

  # 数据处理配置
  processing:
    # 最大样本数量，用于调试，null表示使用全部数据
    max_samples: null
    # 图像尺寸 [height, width] - LORE-TSR ctdet_mid默认尺寸
    image_size: [768, 768]
    # 输入分辨率
    input_res: -1
    # 图像归一化参数 (LORE-TSR标准)
    normalize:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
      to_rgb: true
    # 输出下采样比例
    down_ratio: 4
    # 数据增强配置
    augmentation:
      # 随机裁剪
      not_rand_crop: false
      # 位移增强
      shift: 0.1
      # 缩放增强
      scale: 0.4
      # 旋转增强
      rotate: 0
      # 翻转概率
      flip: 0.5
      # 颜色增强
      no_color_aug: false

  # 数据加载配置
  loader:
    # 数据加载器工作进程数
    num_workers: 4
    # 是否使用pin_memory
    pin_memory: true

# ============================================================================
# 模型配置
# ============================================================================
model:
  # 模型架构名称 - LORE-TSR默认架构
  arch_name: "resfpnhalf_18"
  # 是否使用预训练权重
  pretrained: false
  # 检测头中间层通道数
  head_conv: 64
  # 预训练权重路径（可选）
  load_model: ""
  # 预训练处理器路径（可选）
  load_processor: ""

  # 输出头配置 - 对应LORE-TSR的多任务头
  heads:
    hm: 2      # 热力图通道数（背景+单元格中心）
    wh: 8      # 边界框通道数（4个角点坐标）
    reg: 2     # 偏移通道数（中心点偏移）
    st: 8      # 结构通道数（表格结构信息）
    ax: 256    # 轴向特征通道数（逻辑位置特征）
    cr: 256    # 角点特征通道数（角点回归特征）

# ============================================================================
# 处理器配置 (LORE-TSR特有)
# ============================================================================
processor:
  # 启用2D位置嵌入
  wiz_2dpe: false
  # 启用四角点特征
  wiz_4ps: false
  # 启用堆叠回归器
  wiz_stacking: false
  # 启用配对损失（仅用于有线表格）
  wiz_pairloss: false
  # 启用单元间损失
  wiz_dsloss: false

  # Transformer配置
  tsfm_layers: 6           # Transformer层数
  stacking_layers: 3       # 堆叠层数
  hidden_size: 256         # 隐藏层大小
  input_size: 256          # 输入大小
  output_size: 4           # 输出大小（四个逻辑索引）
  num_heads: 8             # 注意力头数
  att_dropout: 0.1         # 注意力dropout
  max_fmp_size: 256        # 最大特征图大小

  # 检测配置
  K: 100                   # 最大检测数量
  MK: 700                  # 最大关键点数量

# ============================================================================
# 损失函数配置
# ============================================================================
loss:
  # 损失函数类型
  mse_loss: false          # 是否使用MSE损失（否则使用focal loss）
  reg_loss: "l1"           # 回归损失类型：sl1 | l1 | l2

  # 各损失函数权重
  weights:
    hm_weight: 1.0         # 热力图损失权重
    mk_weight: 1.0         # 角点热图损失权重
    wh_weight: 1.0         # 边界框损失权重
    off_weight: 1.0        # 偏移损失权重
    st_weight: 1.0         # 结构损失权重

# ============================================================================
# 训练配置
# ============================================================================
training:
  # 基础训练参数
  epochs: 90
  batch_size: 32
  master_batch_size: -1    # 主GPU批次大小
  val_intervals: 5         # 验证间隔

  # 优化器配置
  optimizer:
    # 优化器类型 (SGD/Adam/AdamW/AdamW_8Bit)
    type: "Adam"
    learning_rate: 1.25e-4

    # Adam系列的特定参数
    adamx:
      beta1: 0.9
      beta2: 0.999
      epsilon: 1e-8
      weight_decay: 0.0

    # SGD特定参数
    sgd:
      momentum: 0.9
      weight_decay: 0.0001

  # 学习率调度器配置
  scheduler:
    # 调度器类型 (constant/step/cosine/linear)
    type: "step"
    # 阶梯式调度参数
    step:
      milestones: [80]        # 学习率衰减节点
      gamma: 0.1              # 衰减因子

# ============================================================================
# 推理配置
# ============================================================================
inference:
  # 翻转测试
  flip_test: false
  # 测试尺度
  test_scales: [1.0]
  # NMS
  nms: true
  # 固定分辨率测试
  fix_res: true
  # 保持原始分辨率
  keep_res: false
  # 阈值配置
  thresh_min: 0.5
  thresh_max: 0.7
  thresh_conf: 0.1
  # 可视化阈值
  vis_thresh: 0.3
  vis_thresh_corner: 0.1

# ============================================================================
# 可视化配置
# ============================================================================
visualization:
  # 是否启用可视化功能
  enabled: true
  # 可视化样本图片路径
  sample_images_dir: "assets/test_images"
  # 每次可视化的样本数量
  max_samples: 10
  # 可视化结果保存路径
  output_dir: null
  # 可视化频率：每N次验证进行一次可视化
  frequency: 1
  # 调试器主题
  debugger_theme: "white"

# ============================================================================
# 分布式训练配置
# ============================================================================
distributed:
  # GPU配置
  gpus: "0"
  # 混合精度训练 (no/fp16/bf16)
  mixed_precision: "no"
  # CUDA基准测试
  not_cuda_benchmark: false
  # 日志记录工具
  report_to: "tensorboard"
  # 跟踪器项目名称
  tracker_project_name: "lore-tsr-training"

# ============================================================================
# 检查点和验证配置
# ============================================================================
checkpoint:
  # 保存配置
  save:
    # 保存所有模型
    save_all: false
    # 主要评估指标
    metric: "loss"
    # 保存检查点的步数间隔
    steps: 2000
    # 每N个epoch保存一次模型
    every_n_epoch: 1
    # 保留的检查点数量
    keep_num: 100

  # 恢复配置
  resume:
    # 是否恢复训练
    resume: false
    # 从检查点恢复训练的路径
    from_checkpoint: null

  # 验证配置
  validation:
    # 验证时使用的批次数量
    num_batches: 10
```

## 📋 下一步预告

**迭代1步骤1.3**：创建训练入口空框架
- 创建train_lore_tsr.py的基础框架
- 集成配置解析和基础的accelerate环境准备
- 验证训练入口能正常运行到空实现占位点

---

**文档版本**：v1.0
**创建日期**：2025-07-18
**当前迭代**：迭代1（基础目录结构和配置系统）
**当前步骤**：步骤1.2（创建配置文件和参数解析）
**验证要求**：配置解析正常、参数覆盖功能正常、OmegaConf兼容
