# LORE-TSR 到 train-anything 迁移开发计划 - 步骤一.三

## 📋 项目概述

本文档基于步骤1.2的成功执行结果，制定LORE-TSR项目迁移到train-anything框架的**迭代1第3步**开发计划。继续遵循"小步快跑、持续验证"的开发模式，专注于训练入口框架的建立。

### 步骤1.2执行结果回顾
✅ **已完成**：配置文件和命令行参数解析
- 完整的lore_tsr_config.yaml配置文件已创建
- 命令行参数解析模块args.py已实现
- 配置覆盖功能和快速调试参数正常工作
- 所有验证测试通过，配置系统完全可用

## 🗺️ 动态迁移蓝图 (更新版)

### 文件迁移映射表和逻辑图

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 当前迭代 | 复杂度 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配：转换为OmegaConf YAML格式 | 迭代1 | **复杂** | `已完成` |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配：适配accelerate框架 | 迭代1,3 | **复杂** | `进行中` |
| `N/A` | `modules/proj_cmd_args/lore_tsr/args.py` | 新建：命令行参数解析模块 | 迭代1 | **复杂** | `已完成` |
| `src/lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 复制保留：模型工厂函数 | 迭代2 | **复杂** | `未开始` |
| `src/lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留：逐行复制保持精度 | 迭代4 | 简单 | `未开始` |
| `src/lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留：Processor组件 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留：Transformer实现 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/networks/fpn_resnet_half.py` | `networks/lore_tsr/backbones/fpn_resnet_half.py` | 复制保留：主要骨干网络 | 迭代2 | 简单 | `未开始` |
| `src/lib/datasets/dataset/table_mid.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | 重构适配：数据集适配器 | 迭代5 | **复杂** | `未开始` |
| `src/lib/utils/post_process.py` | `modules/utils/lore_tsr/post_process.py` | 复制保留：后处理工具 | 迭代11 | 简单 | `未开始` |
| `src/lib/models/networks/DCNv2/` | `external/lore_tsr/DCNv2/` | 复制隔离：可变形卷积 | 迭代7 | 简单 | `未开始` |

### 目标目录结构树 (更新版)

```text
train-anything/
├── configs/table_structure_recognition/lore_tsr/  # [已创建]
│   └── lore_tsr_config.yaml                      # [已创建]
├── training_loops/table_structure_recognition/   # [已创建]
│   └── train_lore_tsr.py                         # [本步骤创建]
├── networks/lore_tsr/                            # [已创建]
│   ├── __init__.py                               # [已创建]
│   ├── lore_tsr_model.py                         # [待创建]
│   ├── lore_tsr_loss.py                          # [待创建]
│   ├── processor.py                              # [待创建]
│   ├── transformer.py                            # [待创建]
│   ├── backbones/                                # [已创建]
│   │   ├── __init__.py                           # [已创建]
│   │   └── (其他骨干网络文件)                      # [待创建]
│   └── heads/                                    # [已创建]
│       ├── __init__.py                           # [已创建]
│       └── lore_tsr_head.py                      # [待创建]
├── my_datasets/table_structure_recognition/      # [已创建]
│   └── (数据集相关文件)                           # [待创建]
├── modules/proj_cmd_args/lore_tsr/               # [已创建]
│   ├── __init__.py                               # [已创建]
│   ├── args.py                                   # [已创建]
│   └── config_parser.py                          # [已创建]
├── modules/utils/lore_tsr/                       # [已创建]
│   ├── __init__.py                               # [已创建]
│   └── (工具函数文件)                             # [待创建]
└── external/lore_tsr/                            # [已创建]
    ├── __init__.py                               # [已创建]
    └── (外部依赖目录)                             # [待创建]
```

## 🎯 迭代1第3步：创建训练入口空框架

### 步骤标题
**迭代1步骤1.3: 创建训练入口空框架**

### 当前迭代
**迭代1：基础目录结构和配置系统**

### 影响文件
本步骤将创建以下文件：
- `training_loops/table_structure_recognition/train_lore_tsr.py` - 训练入口空框架

### 具体操作

#### 1. 创建训练入口文件框架
基于cycle-centernet的训练入口文件，创建LORE-TSR的训练入口空框架，集成已创建的配置解析系统。

**训练入口文件结构设计**：
```python
#!/usr/bin/env python3
"""
LORE-TSR 表格结构识别训练脚本
基于 train-anything 框架的 accelerate 分布式训练实现
"""

import os
import math
import json
import random
import datetime
import warnings
from pathlib import Path
from typing import Tuple, List

# 设置环境变量
os.environ['TORCH_DISTRIBUTED_DEBUG'] = 'DETAIL'

import torch
import torch.nn as nn
import torch.utils.data
from tqdm import tqdm

from accelerate.logging import get_logger
import modules.utils.torch_utils as torch_utils
from modules.utils.log import create_file_logger
from modules.utils.torch_utils import EMAHandler
from modules.utils.optimization import get_scheduler
from modules.proj_cmd_args.lore_tsr.args import parse_args
from modules.utils.train_utils import prepare_training_enviornment_v2
from modules.utils.train_tools import (
    save_state,
    get_optimizer,
    get_random_states,
    set_random_states,
    walk_dataloaders,
    find_latest_checkpoint,
)

# LORE-TSR组件导入（迭代1：空实现占位）
# from networks.lore_tsr import create_lore_tsr_model, create_lore_tsr_loss  # 迭代2,4
# from my_datasets.table_structure_recognition import LoreTsrDataset  # 迭代5

# 使用新的配置系统
config = parse_args()
logger = get_logger(__name__)
file_logger_path = Path(os.path.join(config.basic.output_dir, "logs", "monitors.log"))
os.makedirs(file_logger_path.parent, exist_ok=True)
file_logger = create_file_logger(file_logger_path, "DEBUG")

def main():
    """LORE-TSR训练主入口函数"""
    global config
    if config is None:
        config = parse_args()

    # 准备训练环境
    accelerator, weight_dtype = prepare_training_enviornment_v2(config, logger)

    logger.info("LORE-TSR训练开始")
    logger.info(f"配置: {config}")

    # 迭代1：空实现占位
    logger.info("迭代1步骤1.3：训练入口框架已初始化")
    logger.info("等待后续迭代实现具体功能...")

    # 后续迭代将添加：
    # - 创建模型和EMA (迭代2)
    # - 设置训练组件 (迭代3)
    # - 创建数据加载器 (迭代5)
    # - 运行训练循环 (迭代3)

    logger.info("LORE-TSR训练框架初始化完成")

if __name__ == '__main__':
    main()
```

#### 2. 实现辅助函数空框架
创建各种辅助函数的空实现占位，为后续迭代预留接口。

**辅助函数框架**：
```python
def create_model_and_ema(config, accelerator, weight_dtype):
    """
    创建LORE-TSR模型实例和EMA包装器
    
    迭代1：空实现占位
    迭代2：实现模型创建逻辑
    """
    logger.info("创建模型和EMA（迭代1：空实现）")
    return None, None

def setup_training_components(config, model):
    """
    设置优化器、学习率调度器和损失函数
    
    迭代1：空实现占位
    迭代3：实现训练组件设置
    """
    logger.info("设置训练组件（迭代1：空实现）")
    return None, None, None, None, None, None, None, None

def prepare_dataloaders(config, mode, batch_size, seed=-1):
    """
    准备数据加载器
    
    迭代1：空实现占位
    迭代5：实现数据集适配器
    """
    logger.info(f"准备{mode}数据加载器（迭代1：空实现）")
    return [], []

def run_training_loop(config, model, accelerator, ema_handler, loss_criterion,
                     optimizer, lr_scheduler, weight_dtype, train_loaders, val_loaders,
                     global_step, first_epoch, max_train_steps):
    """
    执行完整的训练循环
    
    迭代1：空实现占位
    迭代3：实现完整训练循环
    """
    logger.info("运行训练循环（迭代1：空实现）")
    return global_step
```

### 受影响的现有模块
- **无影响**：本步骤只创建新文件，不修改train-anything现有模块
- **兼容性**：完全兼容train-anything的accelerate训练框架
- **扩展性**：为后续迭代预留了完整的函数接口

### 复用已有代码
- 参考 `training_loops/table_structure_recognition/train_cycle_centernet_ms.py` 的整体结构
- 复用 train-anything 框架的标准导入和工具函数
- 遵循 accelerate 框架的最佳实践

### 如何验证 (Verification)

#### 验证命令1：训练入口基础运行测试
```bash
# 测试训练入口能正常运行
cd train-anything
python training_loops/table_structure_recognition/train_lore_tsr.py \
    --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml \
    --debug
```

#### 验证命令2：配置解析集成测试
```bash
# 测试配置解析和accelerate环境准备
cd train-anything
python -c "
import sys
sys.path.append('.')
from training_loops.table_structure_recognition.train_lore_tsr import config, logger
print('✅ 配置解析集成正常')
print(f'任务类型: {config.basic.task}')
print(f'模型架构: {config.model.arch_name}')
print(f'输出目录: {config.basic.output_dir}')
print(f'日志器可用: {logger is not None}')
"
```

#### 验证命令3：accelerate环境初始化测试
```bash
# 测试accelerate环境能正确初始化
cd train-anything
python -c "
import sys
sys.path.append('.')
from modules.proj_cmd_args.lore_tsr.args import parse_args
from modules.utils.train_utils import prepare_training_enviornment_v2
from accelerate.logging import get_logger

config = parse_args()
logger = get_logger(__name__)
accelerator, weight_dtype = prepare_training_enviornment_v2(config, logger)

print('✅ accelerate环境初始化成功')
print(f'accelerator类型: {type(accelerator)}')
print(f'权重数据类型: {weight_dtype}')
print(f'设备: {accelerator.device}')
print(f'是否主进程: {accelerator.is_main_process}')
"
```

#### 验证命令4：日志系统测试
```bash
# 测试日志系统正常工作
cd train-anything
python -c "
import sys
import os
from pathlib import Path
sys.path.append('.')
from modules.proj_cmd_args.lore_tsr.args import parse_args
from modules.utils.log import create_file_logger

config = parse_args()
file_logger_path = Path(os.path.join(config.basic.output_dir, 'logs', 'monitors.log'))
os.makedirs(file_logger_path.parent, exist_ok=True)
file_logger = create_file_logger(file_logger_path, 'DEBUG')

file_logger.info('测试日志消息')
print('✅ 日志系统工作正常')
print(f'日志文件路径: {file_logger_path}')
print(f'日志目录存在: {file_logger_path.parent.exists()}')
"
```

### 当前迭代逻辑图

```mermaid
graph TD
    %% 当前迭代：迭代1步骤1.3 - 创建训练入口空框架

    subgraph "源：LORE-TSR/src/main.py"
        direction TB
        S1["参数解析逻辑"]
        S2["训练环境设置"]
        S3["模型创建"]
        S4["数据加载器创建"]
        S5["训练循环执行"]
        S6["模型保存"]
        
        S1 --> S2
        S2 --> S3
        S3 --> S4
        S4 --> S5
        S5 --> S6
    end

    subgraph "目标：train-anything/train_lore_tsr.py"
        direction TB
        T1["配置解析（已实现）"]
        T2["accelerate环境准备"]
        T3["模型创建（空实现占位）"]
        T4["数据加载器（空实现占位）"]
        T5["训练循环（空实现占位）"]
        T6["模型保存（空实现占位）"]
        
        T1 --> T2
        T2 --> T3
        T3 --> T4
        T4 --> T5
        T5 --> T6
    end

    subgraph "框架集成"
        direction LR
        F1["OmegaConf配置系统"]
        F2["accelerate分布式训练"]
        F3["标准日志系统"]
        F4["train-anything工具函数"]
    end

    subgraph "验证检查"
        direction LR
        V1["训练入口运行测试"]
        V2["配置解析集成测试"]
        V3["accelerate环境测试"]
        V4["日志系统测试"]
    end

    %% 迁移映射
    S1 -- "重构适配" --> T1
    S2 -- "重构适配" --> T2
    S3 -- "空实现占位" --> T3
    S4 -- "空实现占位" --> T4
    S5 -- "空实现占位" --> T5
    S6 -- "空实现占位" --> T6

    %% 框架集成
    F1 --> T1
    F2 --> T2
    F3 --> T2
    F4 --> T2

    %% 验证流程
    T1 --> V2
    T2 --> V3
    T3 --> V1
    T4 --> V1
    T5 --> V1
    T6 --> V4
```

## 📝 步骤完成标准

### 功能验收
1. **训练入口可运行**：train_lore_tsr.py能正常运行到空实现占位点
2. **配置解析正常**：能正确加载和解析lore_tsr_config.yaml配置文件
3. **accelerate集成**：accelerate环境能正确初始化，支持分布式训练
4. **日志系统正常**：文件日志和控制台日志都能正常工作

### 技术验收
1. **代码质量**：训练入口文件符合Python代码规范和train-anything框架标准
2. **框架兼容**：完全兼容accelerate框架和train-anything工具函数
3. **扩展性验证**：为后续迭代预留了正确的函数接口和扩展点
4. **错误处理**：基础的错误处理和异常捕获机制

### 集成验收
1. **框架集成**：完全集成到train-anything的训练流程中
2. **配置兼容**：与已创建的配置系统无缝集成
3. **工具复用**：正确使用train-anything框架的标准工具函数
4. **目录规范**：符合train-anything的目录结构和命名规范

## 🚨 风险管理

### 技术风险
1. **导入路径问题**：确保所有导入路径正确，无ImportError
2. **accelerate兼容性**：确保accelerate环境能正确初始化
3. **配置集成问题**：确保与已创建的配置系统正确集成

### 缓解措施
1. **导入验证**：逐一验证所有导入语句，确保路径正确
2. **环境测试**：单独测试accelerate环境初始化
3. **集成测试**：验证配置解析和训练入口的集成

## 📋 下一步预告

**迭代2步骤2.1**：核心模型架构迁移
- 迁移LORE-TSR的骨干网络到networks/lore_tsr/backbones/
- 实现lore_tsr_model.py模型工厂函数
- 创建多任务检测头lore_tsr_head.py
- 验证模型能够正确实例化和前向传播

---

**文档版本**：v1.0  
**创建日期**：2025-07-18  
**当前迭代**：迭代1（基础目录结构和配置系统）  
**当前步骤**：步骤1.3（创建训练入口空框架）  
**验证要求**：训练入口可运行、配置解析正常、accelerate集成、日志系统正常
