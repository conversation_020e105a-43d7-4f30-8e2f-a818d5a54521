# LORE-TSR 迁移编码计划 - 迭代二步骤2.1

## 📋 计划概述

**当前迭代**: 迭代2 - 核心模型架构迁移  
**步骤标识**: 步骤2.1 - 创建模型工厂函数和基础架构适配  
**迁移策略**: 重构适配框架入口  
**预计工期**: 1个工作日  

## 🗂️ 动态迁移蓝图

### 文件迁移映射表

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 当前迭代 | 复杂度 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配：转换为OmegaConf YAML格式 | 迭代1 | **复杂** | `已完成` ✅ |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配：适配accelerate框架 | 迭代1,3 | **复杂** | `部分完成` 🔄 |
| `src/lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 重构适配：模型工厂函数 | 迭代2 | **复杂** | `进行中` 🚧 |
| `src/lib/models/networks/fpn_resnet_half.py` | `networks/lore_tsr/backbones/fpn_resnet_half.py` | 复制保留：主要骨干网络 | 迭代2 | 简单 | `未开始` |
| `src/lib/models/networks/fpn_resnet.py` | `networks/lore_tsr/backbones/fpn_resnet.py` | 复制保留：标准ResNet+FPN架构 | 迭代2 | 简单 | `未开始` |
| `src/lib/models/networks/fpn_mask_resnet_half.py` | `networks/lore_tsr/backbones/fpn_mask_resnet_half.py` | 复制保留：带掩码的半尺寸网络 | 迭代2 | 简单 | `未开始` |
| `src/lib/models/networks/fpn_mask_resnet.py` | `networks/lore_tsr/backbones/fpn_mask_resnet.py` | 复制保留：带掩码的标准网络 | 迭代2 | 简单 | `未开始` |
| `src/lib/models/networks/pose_dla_dcn.py` | `networks/lore_tsr/backbones/pose_dla_dcn.py` | 复制保留：DLA+DCN架构 | 迭代2 | 简单 | `未开始` |
| 检测头逻辑（内嵌在骨干网络中） | `networks/lore_tsr/heads/lore_tsr_head.py` | 重构适配：分离检测头 | 迭代2 | **复杂** | `未开始` |
| `src/lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留：逐行复制保持精度 | 迭代4 | 简单 | `未开始` |
| `src/lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留：Processor组件 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留：Transformer实现 | 迭代6 | **复杂** | `未开始` |

### 目标目录结构树

```text
train-anything/
├── configs/table_structure_recognition/lore_tsr/
│   └── lore_tsr_config.yaml                      # ✅ 已完成
├── training_loops/table_structure_recognition/
│   └── train_lore_tsr.py                         # 🔄 部分完成（空框架）
├── networks/lore_tsr/
│   ├── __init__.py                               # ✅ 已完成
│   ├── lore_tsr_model.py                         # 🚧 步骤2.1目标
│   ├── lore_tsr_loss.py                          # [待创建]
│   ├── processor.py                              # [待创建]
│   ├── transformer.py                            # [待创建]
│   ├── backbones/
│   │   ├── __init__.py                           # ✅ 已完成
│   │   ├── fpn_resnet_half.py                    # [待创建]
│   │   ├── fpn_resnet.py                         # [待创建]
│   │   ├── fpn_mask_resnet_half.py               # [待创建]
│   │   ├── fpn_mask_resnet.py                    # [待创建]
│   │   └── pose_dla_dcn.py                       # [待创建]
│   └── heads/
│       ├── __init__.py                           # ✅ 已完成
│       └── lore_tsr_head.py                      # [待创建]
├── my_datasets/table_structure_recognition/      # [待创建]
├── modules/utils/lore_tsr/                       # [待创建]
├── modules/visualization/                        # [待创建]
└── external/lore_tsr/                            # [待创建]
```

## 🎯 步骤2.1具体任务

### 步骤标题
**迭代2步骤2.1: 创建LORE-TSR模型工厂函数和配置适配层**

### 当前迭代
迭代2 - 核心模型架构迁移

### 影响文件
- **创建**: `networks/lore_tsr/lore_tsr_model.py` - 模型工厂函数
- **更新**: `networks/lore_tsr/__init__.py` - 导出模型工厂函数
- **更新**: `training_loops/table_structure_recognition/train_lore_tsr.py` - 集成模型创建逻辑

### 具体操作

#### 1. 创建模型工厂函数文件
创建 `networks/lore_tsr/lore_tsr_model.py`，实现以下核心功能：

```python
#!/usr/bin/env python3
"""
LORE-TSR 模型工厂函数
基于 train-anything 框架的配置系统，创建 LORE-TSR 模型实例
"""

import torch
import torch.nn as nn
from omegaconf import DictConfig
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class LoreTsrModel(nn.Module):
    """LORE-TSR模型包装器"""
    
    def __init__(self, backbone: nn.Module, arch_name: str, head_config: Dict[str, int]):
        super(LoreTsrModel, self).__init__()
        self.backbone = backbone
        self.arch_name = arch_name
        self.head_config = head_config
        self.num_classes = head_config.get('hm', 2)
        
    def forward(self, x: torch.Tensor):
        """前向传播"""
        return self.backbone(x)
    
    def load_pretrained_weights(self, path: str):
        """加载预训练权重"""
        logger.info(f"加载预训练权重: {path}")
        # 迭代8将实现具体的权重加载逻辑
        pass

def create_lore_tsr_model(config: DictConfig) -> LoreTsrModel:
    """
    创建LORE-TSR模型实例的工厂函数
    
    Args:
        config: OmegaConf配置对象
        
    Returns:
        LoreTsrModel: 完整的LORE-TSR模型实例
    """
    logger.info("创建LORE-TSR模型...")
    
    # 解析模型配置
    model_config = parse_model_config(config)
    
    # 获取骨干网络工厂（迭代2后续步骤实现）
    backbone_factory = get_backbone_factory()
    
    # 创建骨干网络（暂时使用占位符）
    backbone = create_placeholder_backbone(model_config)
    
    # 创建完整模型
    model = LoreTsrModel(
        backbone=backbone,
        arch_name=model_config['arch'],
        head_config=model_config['heads']
    )
    
    # 加载预训练权重
    if config.model.get('load_model', ''):
        model.load_pretrained_weights(config.model.load_model)
    
    logger.info(f"LORE-TSR模型创建完成: {model_config['arch']}")
    return model

def parse_model_config(config: DictConfig) -> Dict[str, Any]:
    """解析train-anything配置格式到LORE-TSR参数格式"""
    model_cfg = config.model
    
    # 解析架构名称和层数
    arch_name = model_cfg.arch_name
    num_layers = int(arch_name.split('_')[-1]) if '_' in arch_name else 18
    arch = arch_name.rsplit('_', 1)[0] if '_' in arch_name else arch_name
    
    return {
        'arch': arch,
        'num_layers': num_layers,
        'heads': dict(model_cfg.heads),
        'head_conv': model_cfg.head_conv,
        'pretrained': model_cfg.get('pretrained', False)
    }

def get_backbone_factory() -> Dict[str, Any]:
    """获取骨干网络工厂函数映射（迭代2后续步骤实现）"""
    # 为迭代2后续步骤预留接口
    return {
        'resfpnhalf': create_placeholder_backbone,
        'resfpn': create_placeholder_backbone,
        'resfpnmaskhalf': create_placeholder_backbone,
        'resfpnmask': create_placeholder_backbone,
        'dla': create_placeholder_backbone,
    }

def create_placeholder_backbone(model_config: Dict[str, Any]) -> nn.Module:
    """创建占位符骨干网络（迭代2后续步骤将替换为真实实现）"""
    class PlaceholderBackbone(nn.Module):
        def __init__(self, heads):
            super().__init__()
            self.heads = heads
            # 简单的占位符网络
            self.conv = nn.Conv2d(3, 64, 3, padding=1)
            self.pool = nn.AdaptiveAvgPool2d((192, 192))
            
            # 为每个检测头创建占位符输出层
            for head, channels in heads.items():
                setattr(self, head, nn.Conv2d(64, channels, 1))
        
        def forward(self, x):
            x = self.conv(x)
            x = self.pool(x)
            
            ret = {}
            for head in self.heads:
                ret[head] = getattr(self, head)(x)
            
            return [ret]
    
    return PlaceholderBackbone(model_config['heads'])
```

#### 2. 更新模块导出
更新 `networks/lore_tsr/__init__.py`：

```python
"""LORE-TSR网络模块"""

from .lore_tsr_model import create_lore_tsr_model, LoreTsrModel

__all__ = [
    'create_lore_tsr_model',
    'LoreTsrModel',
]
```

#### 3. 集成训练入口
更新 `training_loops/table_structure_recognition/train_lore_tsr.py` 中的 `create_model_and_ema()` 函数：

```python
def create_model_and_ema(config, accelerator, model_state_dict, ema_path, weight_dtype, load_state_dict_msg):
    """创建LORE-TSR模型实例和EMA包装器"""
    logger.info("创建LORE-TSR模型...")
    
    # 使用新的模型工厂函数创建模型
    from networks.lore_tsr import create_lore_tsr_model
    model = create_lore_tsr_model(config)
    
    # 加载预训练权重
    if model_state_dict is not None:
        model.load_state_dict(model_state_dict, strict=False)
        logger.info(f"模型权重加载完成: {load_state_dict_msg}")
    
    # 创建EMA处理器
    ema_handler = None
    if config.ema.enabled:
        from modules.utils.ema import EMAHandler
        ema_handler = EMAHandler(model, config.ema.decay)
        if ema_path and os.path.exists(ema_path):
            ema_handler.load_state_dict(torch.load(ema_path))
            logger.info(f"EMA权重加载完成: {ema_path}")
    
    return model, ema_handler
```

### 受影响的现有模块
- **train-anything配置系统**: 完全兼容现有OmegaConf配置格式
- **accelerate框架**: 无影响，模型创建后将正常集成到accelerate流程
- **EMA系统**: 无影响，继续使用现有EMA处理器

### 复用已有代码
- **配置解析**: 复用train-anything的OmegaConf配置系统
- **日志系统**: 复用train-anything的标准日志框架
- **EMA处理**: 复用modules/utils/ema.py的EMA处理器
- **模型包装**: 参考cycle-centernet-ms的模型创建模式

### 如何验证 (Verification)

```shell
# 1. 模型工厂函数语法检查
python -m py_compile networks/lore_tsr/lore_tsr_model.py

# 2. 模型创建功能测试
python -c "
import sys; sys.path.append('.');
from networks.lore_tsr import create_lore_tsr_model;
from modules.proj_cmd_args.lore_tsr.args import parse_args;
config = parse_args();
model = create_lore_tsr_model(config);
print('✅ 模型创建成功');
print(f'模型类型: {type(model).__name__}');
print(f'架构名称: {model.arch_name}');
print(f'检测头配置: {model.head_config}');
"

# 3. 前向传播测试
python -c "
import sys; sys.path.append('.'); import torch;
from networks.lore_tsr import create_lore_tsr_model;
from modules.proj_cmd_args.lore_tsr.args import parse_args;
config = parse_args();
model = create_lore_tsr_model(config);
x = torch.randn(1, 3, 768, 768);
with torch.no_grad(): outputs = model(x);
print('✅ 前向传播成功');
print(f'输出类型: {type(outputs)}');
print(f'输出长度: {len(outputs)}');
print(f'检测头数量: {len(outputs[0])}');
for head, tensor in outputs[0].items():
    print(f'  {head}: {tensor.shape}');
"

# 4. 训练入口集成测试
python -c "
import sys; sys.path.append('.');
from training_loops.table_structure_recognition.train_lore_tsr import create_model_and_ema;
from modules.proj_cmd_args.lore_tsr.args import parse_args;
from accelerate import Accelerator;
config = parse_args();
accelerator = Accelerator();
model, ema_handler = create_model_and_ema(config, accelerator, None, None, torch.float32, '');
print('✅ 训练入口集成成功');
print(f'模型类型: {type(model).__name__}');
print(f'EMA处理器: {ema_handler is not None if config.ema.enabled else \"未启用\"}');
"

# 5. 配置适配验证
python -c "
import sys; sys.path.append('.');
from networks.lore_tsr.lore_tsr_model import parse_model_config;
from modules.proj_cmd_args.lore_tsr.args import parse_args;
config = parse_args();
parsed = parse_model_config(config);
print('✅ 配置适配验证成功');
print(f'架构: {parsed[\"arch\"]}');
print(f'层数: {parsed[\"num_layers\"]}');
print(f'检测头: {parsed[\"heads\"]}');
print(f'头部卷积: {parsed[\"head_conv\"]}');
"
```

### 当前迭代逻辑图

```mermaid
graph TD
    %% 当前迭代：迭代2步骤2.1 - 创建模型工厂函数

    subgraph "Source: LORE-TSR/src/lib/models/model.py"
        direction LR
        model_create["模型创建逻辑"]
        model_config["配置参数处理"]
        model_backbone["骨干网络选择"]
        model_heads["检测头配置"]
    end

    subgraph "Target: train-anything/networks/lore_tsr/"
        direction TB
        T1["lore_tsr_model.py<br/>模型工厂函数"]
        T2["LoreTsrModel<br/>模型包装器"]
        T3["create_lore_tsr_model()<br/>工厂函数"]
        T4["parse_model_config()<br/>配置适配"]
    end

    subgraph "Integration: train_lore_tsr.py"
        direction LR
        I1["create_model_and_ema()<br/>模型创建入口"]
        I2["EMA处理器集成"]
        I3["accelerate框架集成"]
    end

    %% 迁移映射
    model_create -- "Refactor to Factory" --> T3
    model_config -- "Adapt to OmegaConf" --> T4
    model_backbone -- "Placeholder Implementation" --> T2
    model_heads -- "Config Mapping" --> T2

    %% 集成关系
    T1 --> I1
    T3 -.-> I1
    T4 -.-> T3
    T2 -.-> T3

    %% 框架依赖
    I1 -.-> I2
    I1 -.-> I3

    style T1 fill:#e1f5fe
    style I1 fill:#f3e5f5
    style model_create fill:#fff3e0
```

## ✅ 验收标准

### 功能验收
1. **模型实例化成功**: 工厂函数能够根据配置创建模型实例
2. **前向传播正常**: 模型能够处理标准输入，输出格式正确
3. **配置系统集成**: 配置参数能够正确传递到模型组件
4. **训练入口集成**: 训练入口能够使用新的模型工厂函数

### 技术验收
1. **代码质量**: 符合Python代码规范，通过语法检查
2. **接口设计**: 为后续迭代预留了正确的扩展接口
3. **错误处理**: 包含基础的异常捕获和日志记录
4. **文档完整**: 函数和类都有清晰的文档说明

### 集成验收
1. **框架兼容性**: 完全符合train-anything的模型创建规范
2. **配置一致性**: 配置参数映射正确，无遗漏或错误
3. **占位符设计**: 占位符实现合理，便于后续替换
4. **依赖管理**: 所有依赖都正确声明，无隐式依赖

## 🚨 风险管理

### 技术风险
1. **配置映射风险**: 参数映射错误导致模型行为异常
   - **缓解措施**: 详细的配置验证和测试用例
   - **应急方案**: 提供手动配置覆盖机制

2. **占位符实现风险**: 占位符过于简单，影响后续集成
   - **缓解措施**: 设计合理的占位符接口，保持输出格式一致
   - **应急方案**: 快速调整占位符实现

### 集成风险
1. **框架冲突风险**: 与train-anything现有功能产生冲突
   - **缓解措施**: 独立命名空间，充分的兼容性测试
   - **应急方案**: 隔离LORE-TSR相关功能

## 📝 下一步预告

步骤2.1完成后，下一步将是：
- **步骤2.2**: 迁移主要骨干网络fpn_resnet_half.py
- **步骤2.3**: 创建骨干网络工厂函数和导出机制
- **步骤2.4**: 创建检测头框架lore_tsr_head.py

---

**文档版本**: v1.0  
**创建日期**: 2025-07-19  
**当前迭代**: 迭代2步骤2.1  
**预计完成时间**: 1个工作日  
**依赖状态**: 迭代1已完成 ✅  
**验证要求**: 5个验证命令全部通过
