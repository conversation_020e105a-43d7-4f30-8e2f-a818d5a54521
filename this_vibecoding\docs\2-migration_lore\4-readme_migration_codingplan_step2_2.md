# LORE-TSR 迁移编码计划 - 迭代二步骤2.2

## 📋 计划概述

**当前迭代**: 迭代2 - 核心模型架构迁移
**步骤标识**: 步骤2.2 - 迁移所有骨干网络文件并建立完整工厂函数
**迁移策略**: 复制保留核心算法
**预计工期**: 1个工作日

## 🗂️ 动态迁移蓝图

### 文件迁移映射表

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 当前迭代 | 复杂度 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配：转换为OmegaConf YAML格式 | 迭代1 | **复杂** | `已完成` ✅ |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配：适配accelerate框架 | 迭代1,3 | **复杂** | `部分完成` 🔄 |
| `src/lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 重构适配：模型工厂函数 | 迭代2 | **复杂** | `已完成` ✅ |
| `src/lib/models/networks/fpn_resnet_half.py` | `networks/lore_tsr/backbones/fpn_resnet_half.py` | 复制保留：主要骨干网络 | 迭代2 | 简单 | `进行中` 🚧 |
| `src/lib/models/networks/fpn_resnet.py` | `networks/lore_tsr/backbones/fpn_resnet.py` | 复制保留：标准ResNet+FPN架构 | 迭代2 | 简单 | `进行中` 🚧 |
| `src/lib/models/networks/fpn_mask_resnet_half.py` | `networks/lore_tsr/backbones/fpn_mask_resnet_half.py` | 复制保留：带掩码的半尺寸网络 | 迭代2 | 简单 | `进行中` 🚧 |
| `src/lib/models/networks/fpn_mask_resnet.py` | `networks/lore_tsr/backbones/fpn_mask_resnet.py` | 复制保留：带掩码的标准网络 | 迭代2 | 简单 | `进行中` 🚧 |
| `src/lib/models/networks/pose_dla_dcn.py` | `networks/lore_tsr/backbones/pose_dla_dcn.py` | 复制保留：DLA+DCN架构 | 迭代2 | **复杂** | `进行中` 🚧 |
| 检测头逻辑（内嵌在骨干网络中） | `networks/lore_tsr/heads/lore_tsr_head.py` | 重构适配：分离检测头 | 迭代2 | **复杂** | `未开始` |
| `src/lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留：逐行复制保持精度 | 迭代4 | 简单 | `未开始` |
| `src/lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留：Processor组件 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留：Transformer实现 | 迭代6 | **复杂** | `未开始` |

### 目标目录结构树

```text
train-anything/
├── configs/table_structure_recognition/lore_tsr/
│   └── lore_tsr_config.yaml                      # ✅ 已完成
├── training_loops/table_structure_recognition/
│   └── train_lore_tsr.py                         # 🔄 部分完成（空框架）
├── networks/lore_tsr/
│   ├── __init__.py                               # ✅ 已完成
│   ├── lore_tsr_model.py                         # ✅ 已完成
│   ├── lore_tsr_loss.py                          # [待创建]
│   ├── processor.py                              # [待创建]
│   ├── transformer.py                            # [待创建]
│   ├── backbones/
│   │   ├── __init__.py                           # ✅ 已完成
│   │   ├── fpn_resnet_half.py                    # 🚧 步骤2.2目标
│   │   ├── fpn_resnet.py                         # 🚧 步骤2.2目标
│   │   ├── fpn_mask_resnet_half.py               # 🚧 步骤2.2目标
│   │   ├── fpn_mask_resnet.py                    # 🚧 步骤2.2目标
│   │   └── pose_dla_dcn.py                       # 🚧 步骤2.2目标
│   └── heads/
│       ├── __init__.py                           # ✅ 已完成
│       └── lore_tsr_head.py                      # [待创建]
├── my_datasets/table_structure_recognition/      # [待创建]
├── modules/utils/lore_tsr/                       # [待创建]
├── modules/visualization/                        # [待创建]
└── external/lore_tsr/                            # [待创建]
    └── DCNv2/                                    # 🚧 步骤2.2临时占位符
```

## 🎯 步骤2.2具体任务

### 步骤标题
**迭代2步骤2.2: 迁移所有骨干网络文件并建立完整工厂函数映射**

### 当前迭代
迭代2 - 核心模型架构迁移

### 影响文件
- **创建**: `networks/lore_tsr/backbones/fpn_resnet_half.py` - 主要骨干网络实现
- **创建**: `networks/lore_tsr/backbones/fpn_resnet.py` - 标准ResNet+FPN架构
- **创建**: `networks/lore_tsr/backbones/fpn_mask_resnet_half.py` - 带掩码的半尺寸网络
- **创建**: `networks/lore_tsr/backbones/fpn_mask_resnet.py` - 带掩码的标准网络
- **创建**: `networks/lore_tsr/backbones/pose_dla_dcn.py` - DLA+DCN架构
- **创建**: `external/lore_tsr/DCNv2/dcn_v2.py` - DCN临时占位符
- **更新**: `networks/lore_tsr/backbones/__init__.py` - 导出所有骨干网络函数
- **更新**: `networks/lore_tsr/lore_tsr_model.py` - 集成所有真实骨干网络

### 具体操作

#### 1. 创建DCN临时占位符（解决依赖问题）
创建 `external/lore_tsr/DCNv2/dcn_v2.py` 临时占位符：

```python
"""
DCNv2 临时占位符
用于解决pose_dla_dcn.py的依赖问题，迭代7将替换为真实实现
"""

import torch
import torch.nn as nn

class DCN(nn.Module):
    """DCNv2 临时占位符实现"""

    def __init__(self, inplanes, planes, kernel_size=3, stride=1,
                 padding=1, dilation=1, groups=1, deformable_groups=1, bias=False):
        super(DCN, self).__init__()
        # 使用标准卷积作为占位符
        self.conv = nn.Conv2d(inplanes, planes, kernel_size, stride,
                             padding, dilation, groups, bias)

    def forward(self, x):
        return self.conv(x)
```

#### 2. 复制所有骨干网络文件
按照详细设计文档要求，逐行复制以下5个文件：

**A. fpn_resnet_half.py** (404行)
- 源文件: `LORE-TSR/src/lib/models/networks/fpn_resnet_half.py`
- 目标: `networks/lore_tsr/backbones/fpn_resnet_half.py`
- 工厂函数: `get_pose_net_fpn_half`

**B. fpn_resnet.py** (280行)
- 源文件: `LORE-TSR/src/lib/models/networks/fpn_resnet.py`
- 目标: `networks/lore_tsr/backbones/fpn_resnet.py`
- 工厂函数: `get_pose_net_fpn`

**C. fpn_mask_resnet_half.py**
- 源文件: `LORE-TSR/src/lib/models/networks/fpn_mask_resnet_half.py`
- 目标: `networks/lore_tsr/backbones/fpn_mask_resnet_half.py`
- 工厂函数: `get_pose_net_fpn_mask_half`

**D. fpn_mask_resnet.py**
- 源文件: `LORE-TSR/src/lib/models/networks/fpn_mask_resnet.py`
- 目标: `networks/lore_tsr/backbones/fpn_mask_resnet.py`
- 工厂函数: `get_pose_net_fpn_mask`

**E. pose_dla_dcn.py** (496行)
- 源文件: `LORE-TSR/src/lib/models/networks/pose_dla_dcn.py`
- 目标: `networks/lore_tsr/backbones/pose_dla_dcn.py`
- 工厂函数: `get_pose_net_dla`
- 特殊处理: 调整DCNv2导入路径

**复制策略**：
- **逐行复制**：保持所有算法逻辑完全不变
- **最小化修改**：只调整必要的import路径
- **统一文件头**：添加train-anything风格的注释

**需要调整的内容**：
```python
# 统一文件头格式
"""
LORE-TSR [网络名称] 骨干网络
从 LORE-TSR 原始实现逐行复制，保持算法完全一致

原始文件: LORE-TSR/src/lib/models/networks/[原文件名]
迁移策略: 复制保留核心算法
"""

# pose_dla_dcn.py特殊处理DCN导入
# 原始: from .DCNv2.dcn_v2 import DCN
# 修改为: from ....external.lore_tsr.DCNv2.dcn_v2 import DCN
```

#### 3. 更新骨干网络模块导出
更新 `networks/lore_tsr/backbones/__init__.py`，导出所有骨干网络：

```python
"""LORE-TSR骨干网络模块"""

from .fpn_resnet_half import get_pose_net_fpn_half
from .fpn_resnet import get_pose_net_fpn
from .fpn_mask_resnet_half import get_pose_net_fpn_mask_half
from .fpn_mask_resnet import get_pose_net_fpn_mask
from .pose_dla_dcn import get_pose_net_dla

# 完整的骨干网络工厂函数映射（严格按照详细设计文档）
BACKBONE_FACTORY = {
    'resfpnhalf': get_pose_net_fpn_half,
    'resfpn': get_pose_net_fpn,
    'resfpnmaskhalf': get_pose_net_fpn_mask_half,
    'resfpnmask': get_pose_net_fpn_mask,
    'dla': get_pose_net_dla,
}

def get_backbone_factory():
    """获取骨干网络工厂函数映射"""
    return BACKBONE_FACTORY

__all__ = [
    'get_pose_net_fpn_half',
    'get_pose_net_fpn',
    'get_pose_net_fpn_mask_half',
    'get_pose_net_fpn_mask',
    'get_pose_net_dla',
    'get_backbone_factory',
    'BACKBONE_FACTORY',
]
```

#### 4. 集成所有真实骨干网络到模型工厂
更新 `networks/lore_tsr/lore_tsr_model.py` 中的相关函数：

```python
def get_backbone_factory() -> Dict[str, Any]:
    """获取骨干网络工厂函数映射"""
    from .backbones import get_backbone_factory as get_factory
    return get_factory()

def create_lore_tsr_model(config: DictConfig) -> LoreTsrModel:
    """创建LORE-TSR模型实例的工厂函数"""
    logger.info("创建LORE-TSR模型...")

    # 解析模型配置
    model_config = parse_model_config(config)

    # 获取骨干网络工厂
    backbone_factory = get_backbone_factory()

    # 创建真实骨干网络（现在支持所有架构）
    arch = model_config['arch']
    if arch in backbone_factory:
        backbone = backbone_factory[arch](
            num_layers=model_config['num_layers'],
            heads=model_config['heads'],
            head_conv=model_config['head_conv']
        )
        logger.info(f"使用真实骨干网络: {arch}")
    else:
        # 不应该到达这里，因为所有架构都已实现
        raise ValueError(f"不支持的架构: {arch}")

    # 创建完整模型
    model = LoreTsrModel(
        backbone=backbone,
        arch_name=model_config['arch'],
        head_config=model_config['heads']
    )

    # 加载预训练权重
    if config.model.get('load_model', ''):
        model.load_pretrained_weights(config.model.load_model)

    logger.info(f"LORE-TSR模型创建完成: {model_config['arch']}")
    return model

# 移除占位符函数，因为所有架构都已实现
```

### 受影响的现有模块
- **无影响**: 这是纯粹的新增功能，不影响train-anything现有模块
- **完全替换**: 移除占位符机制，所有架构都使用真实实现
- **配置兼容**: 完全兼容现有的lore_tsr_config.yaml配置，支持所有架构

### 复用已有代码
- **PyTorch标准模块**: 复用torch.nn, torch.nn.functional等标准组件
- **模型工厂模式**: 复用步骤2.1建立的工厂函数框架
- **配置解析**: 复用现有的parse_model_config函数

### 如何验证 (Verification)

```shell
# 1. 所有骨干网络文件语法检查
python -m py_compile networks/lore_tsr/backbones/fpn_resnet_half.py
python -m py_compile networks/lore_tsr/backbones/fpn_resnet.py
python -m py_compile networks/lore_tsr/backbones/fpn_mask_resnet_half.py
python -m py_compile networks/lore_tsr/backbones/fpn_mask_resnet.py
python -m py_compile networks/lore_tsr/backbones/pose_dla_dcn.py
python -m py_compile external/lore_tsr/DCNv2/dcn_v2.py

# 2. 完整骨干网络工厂测试
python -c "
import sys; sys.path.append('.');
from networks.lore_tsr.backbones import get_backbone_factory;
factory = get_backbone_factory();
print('✅ 骨干网络工厂导入成功');
print(f'支持的架构: {list(factory.keys())}');
for arch, func in factory.items():
    print(f'  {arch}: {func.__name__ if func else \"None\"}');
"

# 3. 所有架构创建测试
python -c "
import sys; sys.path.append('.'); import torch;
from networks.lore_tsr.backbones import get_backbone_factory;
heads = {'hm': 2, 'wh': 8, 'reg': 2, 'st': 8, 'ax': 256, 'cr': 256};
factory = get_backbone_factory();
for arch, func in factory.items():
    try:
        if arch == 'dla':
            backbone = func(34, heads, 64)  # DLA使用34层
        else:
            backbone = func(18, heads, 64)  # ResNet使用18层
        print(f'✅ {arch}: {type(backbone).__name__}');
    except Exception as e:
        print(f'❌ {arch}: {str(e)}');
"

# 4. 多架构前向传播测试
python -c "
import sys; sys.path.append('.'); import torch;
from networks.lore_tsr.backbones import get_backbone_factory;
heads = {'hm': 2, 'wh': 8, 'reg': 2, 'st': 8, 'ax': 256, 'cr': 256};
factory = get_backbone_factory();
x = torch.randn(1, 3, 768, 768);
for arch, func in factory.items():
    try:
        if arch == 'dla':
            backbone = func(34, heads, 64)
        else:
            backbone = func(18, heads, 64)
        with torch.no_grad(): outputs = backbone(x);
        print(f'✅ {arch}: 输出形状正确，检测头数量={len(outputs[0])}');
    except Exception as e:
        print(f'❌ {arch}: {str(e)}');
"

# 5. 配置驱动的模型创建测试（测试所有架构）
python -c "
import sys; sys.path.append('.');
from networks.lore_tsr import create_lore_tsr_model;
from modules.proj_cmd_args.lore_tsr.args import parse_args;
architectures = ['resfpnhalf_18', 'resfpn_18', 'resfpnmaskhalf_18', 'resfpnmask_18', 'dla_34'];
for arch in architectures:
    try:
        config = parse_args();
        config.model.arch_name = arch;
        model = create_lore_tsr_model(config);
        print(f'✅ {arch}: 模型创建成功，骨干网络={type(model.backbone).__name__}');
    except Exception as e:
        print(f'❌ {arch}: {str(e)}');
"

# 6. 端到端多架构测试
python -c "
import sys; sys.path.append('.'); import torch;
from networks.lore_tsr import create_lore_tsr_model;
from modules.proj_cmd_args.lore_tsr.args import parse_args;
architectures = ['resfpnhalf_18', 'resfpn_18', 'resfpnmaskhalf_18', 'resfpnmask_18', 'dla_34'];
x = torch.randn(1, 3, 768, 768);
for arch in architectures:
    try:
        config = parse_args();
        config.model.arch_name = arch;
        model = create_lore_tsr_model(config);
        with torch.no_grad(): outputs = model(x);
        print(f'✅ {arch}: 端到端测试成功，输出形状正确');
    except Exception as e:
        print(f'❌ {arch}: {str(e)}');
"
```

### 当前迭代逻辑图

```mermaid
graph TD
    %% 当前迭代：迭代2步骤2.2 - 迁移所有骨干网络

    subgraph "Source: LORE-TSR/src/lib/models/networks/"
        direction TB
        S1["fpn_resnet_half.py<br/>主要骨干网络"]
        S2["fpn_resnet.py<br/>标准ResNet+FPN"]
        S3["fpn_mask_resnet_half.py<br/>带掩码半尺寸"]
        S4["fpn_mask_resnet.py<br/>带掩码标准"]
        S5["pose_dla_dcn.py<br/>DLA+DCN架构"]
        S6["DCNv2/dcn_v2.py<br/>可变形卷积"]
    end

    subgraph "Target: train-anything/networks/lore_tsr/backbones/"
        direction TB
        T1["fpn_resnet_half.py"]
        T2["fpn_resnet.py"]
        T3["fpn_mask_resnet_half.py"]
        T4["fpn_mask_resnet.py"]
        T5["pose_dla_dcn.py"]
        T6["__init__.py<br/>完整工厂映射"]
    end

    subgraph "DCN Placeholder: external/lore_tsr/"
        direction LR
        P1["DCNv2/dcn_v2.py<br/>临时占位符"]
    end

    subgraph "Integration: lore_tsr_model.py"
        direction LR
        I1["get_backbone_factory()<br/>获取完整工厂"]
        I2["create_lore_tsr_model()<br/>支持所有架构"]
        I3["移除占位符机制<br/>全部真实实现"]
    end

    %% 迁移映射
    S1 -- "Copy Preserve" --> T1
    S2 -- "Copy Preserve" --> T2
    S3 -- "Copy Preserve" --> T3
    S4 -- "Copy Preserve" --> T4
    S5 -- "Copy Preserve" --> T5
    S6 -- "Temporary Placeholder" --> P1

    %% 工厂函数集成
    T1 -.-> T6
    T2 -.-> T6
    T3 -.-> T6
    T4 -.-> T6
    T5 -.-> T6

    %% 依赖关系
    T5 -.-> P1
    T6 --> I1
    I1 --> I2
    I2 --> I3

    %% 验证流程
    T6 -.-> V1["完整工厂测试"]
    I2 -.-> V2["多架构测试"]
    I3 -.-> V3["端到端验证"]

    style T6 fill:#e8f5e8
    style I2 fill:#f3e5f5
    style P1 fill:#fff3e0
```

## ✅ 验收标准

### 功能验收
1. **所有骨干网络实例化成功**: 5个骨干网络都能正确创建实例
2. **多架构前向传播正常**: 所有架构都能处理标准输入，输出格式正确
3. **完整工厂函数**: BACKBONE_FACTORY支持所有配置的架构
4. **配置驱动创建**: 支持通过arch_name配置创建任意架构的模型
5. **DCN依赖解决**: pose_dla_dcn.py能够正常导入和运行

### 技术验收
1. **代码质量**: 所有复制的代码保持原有结构，符合Python规范
2. **算法一致性**: 所有计算逻辑与原LORE-TSR完全相同
3. **接口统一**: 所有工厂函数接口保持一致
4. **依赖处理**: DCN占位符能够正常工作，不影响功能

### 集成验收
1. **框架兼容性**: 完全符合train-anything的模块组织规范
2. **配置完整性**: 支持详细设计文档中的所有架构配置
3. **无占位符依赖**: 移除所有占位符机制，使用真实实现
4. **为步骤2.3准备**: 为检测头创建预留清晰接口

## 🚨 风险管理

### 技术风险
1. **算法一致性风险**: 复制5个文件过程中可能引入差异
   - **缓解措施**: 逐行复制，每个文件都进行严格验证
   - **应急方案**: 保留原始文件作为参考，单独回滚问题文件

2. **DCN依赖风险**: 占位符实现可能影响pose_dla_dcn功能
   - **缓解措施**: 创建功能等价的占位符，充分测试
   - **应急方案**: 临时禁用dla架构，等待迭代7

3. **多架构兼容性风险**: 不同架构可能有细微差异
   - **缓解措施**: 每个架构都进行独立测试
   - **应急方案**: 单独处理有问题的架构

### 集成风险
1. **工厂函数复杂性风险**: 5个架构同时集成可能产生冲突
   - **缓解措施**: 按照详细设计文档的精确映射实现
   - **应急方案**: 逐个架构调试，隔离问题

2. **配置映射风险**: 架构名称映射错误
   - **缓解措施**: 严格按照详细设计文档的配置规范
   - **应急方案**: 提供配置验证工具

## 📝 下一步预告

步骤2.2完成后，迭代2的剩余步骤：
- **步骤2.3**: 创建检测头框架lore_tsr_head.py
- **步骤2.4**: 实现训练入口中的模型创建和初始化逻辑

步骤2.2是迭代2的核心步骤，完成后将建立完整的骨干网络体系，为后续步骤奠定坚实基础。

---

**文档版本**: v2.0
**创建日期**: 2025-07-19
**修订说明**: 严格遵循详细设计文档，迁移所有骨干网络
**当前迭代**: 迭代2步骤2.2
**预计完成时间**: 1个工作日
**依赖状态**: 步骤2.1已完成 ✅
**验证要求**: 6个验证命令全部通过，支持所有架构
