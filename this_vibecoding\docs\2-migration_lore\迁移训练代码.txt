接下来，我需要完成A项目训练代码迁移到B项目的重构性工作，具体如下：

A, 待迁移目录：
- 这个是有关Cycle-CenterNet训练的项目代码目录 @Cycle-CenterNet
- 以及与Cycle-CenterNet相关的代码解读报告 @vibe_coding/1_code_analysis/cycle_centernet_training_call_chain.md

B, 目标目录：
- 这个是有关train-anything训练的项目代码目录 @train-anything
- 以及我给了其中一个训练例子的入口，包含代码解读分析报告，辅助你快速理解项目代码层级，和模块间的协作。代码解读分析报告如下：
  @vibe_coding/1_code_analysis/train_anything_lama_denoising_analysis.md

我预期的迁移结果：
整体需要新增 table_structure_recognition 相关的，如：
- cmd_scripts 新增 table_structure_recognition
- training_loops 新增 table_structure_recognition
- networks 新增 cycle_centernet
- my_datasets 新增 table_structure_recognition
- configs 新增 table_structure_recognition

训练代码保持用 accelerate 封装，具体优化器，迭代方式，前馈过程，反馈过程，损失计算 请务必和原A项目保持一致，不要生编硬造

请你基于规则 @.cursor/rules/2-analysis-prd.mdc 与我共同讨论确定需求文档，文档需要单独命名并输出到 @vibe_coding/1_prd_analysis


---

1. 数据集和标注格式
Cycle-CenterNet原本使用的是什么格式的数据集？（COCO格式、自定义格式？）
答：自定义格式，例如： @vibe_coding/my_chat_history/1_table_annotation.json
是否需要保持原有的数据预处理pipeline，还是需要适配新的数据格式？
答：尽量维持原有数据预处理pipeline的物理含义，以此适配我的新的数据格式

2. 模型架构细节
DLA-34骨干网络是否需要完全迁移，还是可以复用train-anything中已有的backbone？
答：完全迁移

CycleCenterNetHead的4个预测分支（中心点热图、偏移、中心到顶点、顶点到中心）是否都需要保留？
答：忠于原创，保持一致

是否需要支持多种表格复杂度（简单表格vs复杂表格）的训练？
答：以我标注格式为准

3. 训练策略和配置
原项目使用SGD优化器，是否需要在新框架中也保持SGD，还是可以支持Adam等其他优化器？
答：都支持

学习率调度策略（150 epoch训练）是否需要完全保持？
答：默认保持

是否需要支持EMA（指数移动平均）模型维护？
答：原项目有你就保留，没有不要硬造

4. 评估和验证
表格结构识别任务需要什么评估指标？（mAP、IoU、表格结构准确率？）
答：和原项目评估指标一致，不要生编硬造

验证频率和保存策略有什么特殊要求？
答：参考原项目，不要生编硬造

是否需要支持可视化验证结果（如表格结构解析图）？
答：需要

5. 部署和推理需求
答：不要关心

6. 项目扩展性
除了CenterNet架构，未来是否计划支持其他表格识别模型？
答：会支持，替换模型，修改损失输出的方式，其它流程保持一致

是否需要设计通用的表格识别训练接口？
答：最后考虑拓展性，未来最多替换模型，即预留好 损失计算 的拓展性