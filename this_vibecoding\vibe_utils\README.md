# COCO格式数据集分析工具

本目录包含用于分析和处理COCO格式数据集的Python脚本。

## 文件说明

### 1. `WTW_COCO_Fields_Analysis_Report.md` - 字段详细解读报告
**核心文档**：基于联网检索和样本分析的完整字段解读报告
- 详细解释每个字段的含义和作用
- 特别说明 `logic_axis` 字段的TSR应用价值
- 包含技术背景、应用场景和使用建议
- 提供实际样本的字段值分析

### 2. `analyze_coco_dataset.py` - 完整数据集分析工具
功能：
- 分析COCO格式JSON文件的完整结构
- 统计图像、标注、类别信息
- 生成详细的分析报告
- 提取样本数据（两种格式）

使用方法：
```bash
python analyze_coco_dataset.py --json_file WTW-coco-test.json -o analysis_output -n 2
```

参数：
- `--json_file`: COCO格式的JSON标注文件路径
- `-o, --output`: 输出目录路径（默认：analysis_output）
- `-n, --num-samples`: 提取的样本数量（默认：2）

输出文件：
- `analysis_report.txt`: 详细分析报告
- `sample_data.json`: 分析格式的样本数据
- `coco_format_samples.json`: 保持原始COCO格式的样本数据
- `full_stats.json`: 完整统计信息

### 3. `extract_coco_samples.py` - 样本提取专用工具
功能：
- 从大型COCO数据集中提取指定数量的样本
- 保持原始COCO格式不变
- 包含完整的图像信息和相关标注
- 可选的详细分析功能

使用方法：
```bash
python extract_coco_samples.py --input WTW-coco-test.json --output wtw_samples.json --num-samples 2 --analyze
```

参数：
- `--input`: 输入的COCO格式JSON文件路径
- `--output`: 输出的样本文件路径
- `-n, --num-samples`: 提取的样本数量（默认：2）
- `--analyze`: 分析生成的样本文件（可选）

## 数据集格式说明

> 📖 **详细字段解读请参考**: [`WTW_COCO_Fields_Analysis_Report.md`](./WTW_COCO_Fields_Analysis_Report.md)

### WTW-coco-test.json 结构分析
```
顶层键: ['images', 'type', 'annotations', 'categories']

- images: 3644个图像
  - 每个图像包含: id, file_name, width, height
  
- annotations: 348000个标注
  - 每个标注包含: segmentation, logic_axis, area, iscrowd, ignore, image_id, bbox, category_id, id
  
- categories: 0个类别（空列表）

- type: "instances"
```

### 样本数据特点
- 图像1 (car-invoice-img00061.jpg): 1024x768, 52个标注
- 图像2 (01fbcfdc3cf996c0ec57d04d7ac0d970683051d1.jpg): 1145x753, 664个标注
- 所有标注都属于类别ID 1
- 每个标注包含分割多边形、边界框、面积等信息
- 包含特殊字段：logic_axis（逻辑轴信息，用于表格结构识别）

> 💡 **关键创新**: `logic_axis` 字段实现了表格单元格的逻辑位置回归，格式为 `[row_start, row_end, col_start, col_end]`，支持合并单元格的精确表示。

## 使用示例

### 快速提取样本
```bash
# 提取2个样本到wtw_samples.json
python extract_coco_samples.py --input WTW-coco-test.json --output wtw_samples.json

# 提取5个样本并进行分析
python extract_coco_samples.py --input WTW-coco-test.json --output wtw_5samples.json -n 5 --analyze
```

### 完整数据集分析
```bash
# 分析数据集并生成报告
python analyze_coco_dataset.py --json_file WTW-coco-test.json -o analysis_results

# 提取更多样本进行分析
python analyze_coco_dataset.py --json_file WTW-coco-test.json -o analysis_results -n 10
```

## 输出文件说明

### COCO格式样本文件结构
```json
{
  "images": [
    {
      "id": 20180010968,
      "file_name": "car-invoice-img00061.jpg",
      "width": 1024,
      "height": 768
    }
  ],
  "annotations": [
    {
      "segmentation": [[128.0, 165.0, 236.0, 170.0, 233.0, 288.0, 124.0, 284.0]],
      "logic_axis": [[0.0, 0.0, 0.0, 0.0]],
      "area": 13776.0,
      "iscrowd": 0,
      "ignore": 0,
      "image_id": 20180010968,
      "bbox": [124.0, 165.0, 112.0, 123.0],
      "category_id": 1,
      "id": 1111517
    }
  ],
  "categories": [],
  "type": "instances"
}
```

## 注意事项

1. **内存使用**: 原始文件较大（348000个标注），加载时需要足够内存
2. **格式保持**: 样本文件完全保持原始COCO格式，可直接用于训练
3. **标注完整性**: 提取的样本包含选定图像的所有相关标注
4. **类别信息**: 原始数据集的categories为空，但保留在样本中以维持格式一致性

## 依赖要求

- Python 3.6+
- 标准库：json, os, sys, argparse, collections

无需额外安装第三方库。
