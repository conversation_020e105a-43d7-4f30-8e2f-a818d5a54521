# WTW COCO样本数据字段详细分析报告

## 📋 数据集概述

本数据集是一个**表格结构识别(TSR - Table Structure Recognition)**专用的COCO格式数据集，基于标准COCO格式进行了扩展，增加了表格单元格的逻辑位置信息支持。数据集主要用于训练和评估表格结构识别模型。

**数据集特征**：
- 格式：扩展COCO格式
- 应用领域：表格结构识别、文档理解
- 兼容框架：LORE/LORE++表格结构识别框架
- 样本类型：发票、表格文档图像

## 🏗️ 顶层结构字段详解

### 1. `images` - 图像信息数组
**字段类型**：标准COCO字段  
**作用**：存储数据集中所有图像的基本元数据信息

#### 子字段说明：
```json
{
  "id": 20180010968,                    // 图像唯一标识符
  "file_name": "car-invoice-img00061.jpg", // 图像文件名
  "width": 1024,                        // 图像宽度（像素）
  "height": 768                         // 图像高度（像素）
}
```

**字段详解**：
- **`id`**: 整数类型，图像的唯一标识符，用于建立与标注的关联关系
- **`file_name`**: 字符串类型，图像文件的完整文件名，包含扩展名
- **`width`**: 整数类型，图像的像素宽度
- **`height`**: 整数类型，图像的像素高度

### 2. `annotations` - 标注信息数组
**字段类型**：扩展COCO字段  
**作用**：存储每个表格单元格的详细标注信息，包括空间位置和逻辑位置

#### 标准COCO字段：

##### 基础标识字段：
```json
{
  "id": 1111517,           // 标注唯一标识符
  "image_id": 20180010968, // 关联的图像ID
  "category_id": 1         // 类别ID（统一为1，表示表格单元格）
}
```

##### 控制字段：
```json
{
  "iscrowd": 0,  // 是否为群体标注 (0=单个对象, 1=群体对象)
  "ignore": 0    // 训练时是否忽略 (0=使用, 1=忽略)
}
```

##### 几何信息字段：

**`bbox` - 边界框**：
```json
"bbox": [124.0, 165.0, 112.0, 123.0]
```
- 格式：`[x, y, width, height]`
- `x, y`：左上角坐标（像素）
- `width, height`：边界框的宽度和高度（像素）

**`segmentation` - 分割多边形**：
```json
"segmentation": [
  [128.0, 165.0, 236.0, 170.0, 233.0, 288.0, 124.0, 284.0]
]
```
- 格式：`[[x1, y1, x2, y2, x3, y3, x4, y4, ...]]`
- 定义单元格的精确边界轮廓
- 通常为4个点组成的四边形，按顺序连接形成封闭多边形

**`area` - 区域面积**：
```json
"area": 13776.0
```
- 类型：浮点数
- 单位：像素平方
- 表示标注区域的总面积

#### 🧠 **特殊扩展字段 - `logic_axis`**

**这是TSR数据集的核心创新字段！**

```json
"logic_axis": [
  [0.0, 0.0, 0.0, 0.0]
]
```

**字段含义**：表示表格单元格在逻辑表格结构中的位置坐标

**格式定义**：`[[row_start, row_end, col_start, col_end]]`

**参数详解**：
- **`row_start`**: 单元格起始行索引（从0开始）
- **`row_end`**: 单元格结束行索引（从0开始）
- **`col_start`**: 单元格起始列索引（从0开始）
- **`col_end`**: 单元格结束列索引（从0开始）

**实际应用示例**：

| logic_axis值 | 含义 | 表格位置描述 |
|-------------|------|-------------|
| `[0.0, 0.0, 0.0, 0.0]` | 第1行第1列的单个单元格 | 表格左上角单元格 |
| `[1.0, 2.0, 0.0, 0.0]` | 跨越第2-3行，第1列 | 垂直合并单元格 |
| `[0.0, 0.0, 1.0, 3.0]` | 第1行，跨越第2-4列 | 水平合并单元格 |
| `[2.0, 3.0, 1.0, 2.0]` | 跨越第3-4行，第2-3列 | 矩形合并单元格 |

**技术背景**：
基于LORE (LOgical location REgression network) 论文的研究成果，该字段实现了表格结构识别中的**逻辑位置回归**，使模型能够同时学习：
1. **空间特征**：单元格在图像中的像素位置
2. **逻辑特征**：单元格在表格结构中的行列关系

### 3. `categories` - 类别信息数组
**字段类型**：标准COCO字段  
**当前状态**：空数组 `[]`

**说明**：
- 在当前数据集中为空，表示所有标注使用统一的类别ID（category_id = 1）
- 标准COCO格式中通常包含类别名称、ID和超类别信息
- 对于表格结构识别任务，所有单元格被视为同一类别

### 4. `type` - 数据集类型
**字段类型**：标准COCO字段  
**值**：`"instances"`

**含义**：表示这是一个实例分割/检测数据集，每个标注代表一个独立的对象实例。


## 🔧 技术特点与优势

### 1. **双重定位系统**
- **空间定位**：通过 `bbox` 和 `segmentation` 提供像素级精确定位
- **逻辑定位**：通过 `logic_axis` 提供表格结构级语义定位

### 2. **精确边界描述**
- 每个单元格都有精确的多边形轮廓描述
- 支持不规则形状的单元格边界
- 适应表格中的倾斜、变形等情况

### 3. **合并单元格支持**
- 通过 `logic_axis` 完美支持跨行跨列的合并单元格
- 保持表格逻辑结构的完整性
- 便于后续的表格重建和信息提取

### 4. **框架兼容性**
- 完全兼容LORE/LORE++表格结构识别框架
- 支持逻辑位置回归训练范式
- 可直接用于最新的TSR模型训练

