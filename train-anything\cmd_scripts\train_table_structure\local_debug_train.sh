#!/bin/bash

# 程序调度环境设置
export PYTHONPATH=.

export TRAIN_CFG=configs/table_structure_recognition/cycle_centernet/cycle_centernet_config.yaml
export OUTPUT_DIR=/aipdf-mlp/xelawk/training_outputs/tsr_training/cycle-centernet/release_v202507101818

# 任务启动入口，配置参考：单卡 RTX 4090，分辨率 1024x1024，batch size 设置为 16
accelerate launch training_loops/table_structure_recognition/train_cycle_centernet.py -c ${TRAIN_CFG} \
           -o basic.output_dir=${OUTPUT_DIR} \
              training.gradient.clip_norm=false \
              training.gradient.clip_value=35 \
              training.batch_size=16 \
              training.epochs=200 \
              data.loader.num_workers=32