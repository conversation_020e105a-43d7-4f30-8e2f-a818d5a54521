"""
DCNv2 临时占位符实现
用于解决pose_dla_dcn.py的依赖问题，迭代7将替换为真实实现

原始文件: LORE-TSR/src/lib/models/networks/DCNv2/dcn_v2.py
迁移策略: 临时占位符，使用标准卷积替代可变形卷积
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class DCN(nn.Module):
    """DCNv2 临时占位符实现"""

    def __init__(self, inplanes, planes, kernel_size=3, stride=1,
                 padding=1, dilation=1, groups=1, deformable_groups=1, bias=False):
        """
        初始化DCN占位符
        
        Args:
            inplanes: 输入通道数
            planes: 输出通道数
            kernel_size: 卷积核大小
            stride: 步长
            padding: 填充
            dilation: 膨胀
            groups: 分组
            deformable_groups: 可变形分组（占位符中忽略）
            bias: 是否使用偏置
        """
        super(DCN, self).__init__()
        
        # 使用标准卷积作为占位符
        self.conv = nn.Conv2d(
            inplanes, planes, 
            kernel_size=kernel_size, 
            stride=stride,
            padding=padding, 
            dilation=dilation, 
            groups=groups, 
            bias=bias
        )
        
        # 保存参数以便调试
        self.inplanes = inplanes
        self.planes = planes
        self.kernel_size = kernel_size
        self.stride = stride
        self.padding = padding
        self.dilation = dilation
        self.groups = groups
        self.deformable_groups = deformable_groups

    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量
            
        Returns:
            输出张量
        """
        return self.conv(x)


# 为了兼容性，提供其他可能需要的类
class DCNv2(DCN):
    """DCNv2别名"""
    pass


class DeformConv2d(DCN):
    """可变形卷积别名"""
    pass


# 提供一些可能需要的函数
def dcn_v2_conv(input, weight, bias=None, stride=1, padding=0, dilation=1, groups=1, deformable_groups=1):
    """DCN v2 卷积函数占位符"""
    # 使用标准卷积替代
    return F.conv2d(input, weight, bias, stride, padding, dilation, groups)


# 导出所有可能需要的符号
__all__ = [
    'DCN', 
    'DCNv2', 
    'DeformConv2d', 
    'dcn_v2_conv'
]
