#!/usr/bin/env python3
"""
LORE-TSR 命令行参数解析模块
基于 train-anything 框架的 OmegaConf 配置系统

迭代1：基础配置解析实现

Time: 2025-07-18
Author: LORE-TSR Migration Team
Description: 命令行参数解析模块，基于train-anything框架的配置解析系统

使用示例:
    # 基本使用
    python train_lore_tsr.py --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml

    # 覆盖特定参数 (支持多种格式)
    # 格式1: 多个-o参数
    python train_lore_tsr.py \\
        --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml \\
        -o training.epochs=100 \\
        -o training.batch_size=16 \\
        -o data.paths.train_data_dir=/path/to/data

    # 格式2: 单个-o后跟多个参数
    python train_lore_tsr.py \\
        --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml \\
        -o training.epochs=100 training.batch_size=16 basic.debug=1

    # 调试模式
    python train_lore_tsr.py \\
        --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml \\
        --debug

    # 干运行模式（只可视化数据，不训练）
    python train_lore_tsr.py \\
        --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml \\
        --dry-run
"""

import argparse
import sys
from pathlib import Path
from omegaconf import DictConfig

# 导入LORE-TSR的配置解析器
from .config_parser import load_config


def preprocess_args(args=None):
    """
    预处理命令行参数，支持灵活的 -o 参数格式
    
    将原始命令行参数转换为标准的 argparse 格式。
    支持在单个 -o 后跟多个 key=value 参数。
    
    Args:
        args: 命令行参数列表，默认使用 sys.argv[1:]
        
    Returns:
        list: 预处理后的参数列表
    """
    if args is None:
        args = sys.argv[1:]
    
    processed_args = []
    i = 0
    
    while i < len(args):
        arg = args[i]
        
        if arg in ["-o", "--override"]:
            # 遇到 -o 参数
            processed_args.append(arg)
            i += 1
            
            # 收集后续的所有 key=value 参数
            override_values = []
            
            while i < len(args):
                next_arg = args[i]
                
                # 如果是新的选项参数（以-开头且不是key=value格式），停止收集
                if next_arg.startswith('-') and '=' not in next_arg:
                    break
                    
                # 如果包含=号，认为是 key=value 格式
                if '=' in next_arg:
                    override_values.append(next_arg)
                    i += 1
                else:
                    # 不是 key=value 格式，停止收集
                    break
            
            # 将收集到的参数合并为一个字符串（用空格分隔）
            if override_values:
                combined_value = ' '.join(override_values)
                processed_args.append(combined_value)
            else:
                # 没有找到任何覆盖参数，报错
                raise ValueError(f"Missing value for {arg} option")
                
        else:
            # 非 -o 参数，直接添加
            processed_args.append(arg)
            i += 1
    
    return processed_args


def parse_args() -> DictConfig:
    """
    解析LORE-TSR训练的命令行参数和配置文件

    这是基于train-anything框架的配置解析实现，直接返回层级配置对象。
    训练脚本应该直接使用config.xxx.xxx的方式访问配置。

    Returns:
        DictConfig: 完整的训练配置对象
    """
    # 预处理命令行参数
    processed_args = preprocess_args()
    
    parser = argparse.ArgumentParser(
        description="LORE-TSR 表格结构识别训练",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基本使用
  python train_lore_tsr.py --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml
  
  # 覆盖参数 - 格式1: 多个-o参数
  python train_lore_tsr.py --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml -o training.epochs=100 -o training.batch_size=16
  
  # 覆盖参数 - 格式2: 单个-o后跟多个参数
  python train_lore_tsr.py --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml -o training.epochs=100 training.batch_size=16

  # 调试模式
  python train_lore_tsr.py --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml --debug

  # 干运行模式（只可视化数据，不训练）
  python train_lore_tsr.py --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml --dry-run
        """
    )

    # 基础参数
    parser.add_argument(
        "--config",
        type=str,
        default="configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml",
        help="配置文件路径"
    )

    parser.add_argument(
        "-o", "--override",
        action="append",
        default=[],
        help="覆盖配置参数，格式：key=value (可多次使用，或在单个-o后跟多个参数)"
    )

    # 快速调试参数
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式"
    )

    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="启用干运行模式（只可视化数据，不训练）"
    )

    # 使用预处理后的参数进行解析
    args = parser.parse_args(processed_args)

    # 预处理覆盖参数
    overrides = _preprocess_override_args(args.override)

    # 添加命令行快速参数到覆盖列表
    if args.debug:
        overrides.append("basic.debug=1")

    if args.dry_run:
        overrides.append("debug_visualization.dry_run=true")

    # 加载配置文件并应用覆盖
    config = load_config(args.config, overrides)

    return config


def _preprocess_override_args(override_args: list) -> list:
    """
    预处理覆盖参数，支持多种格式

    Args:
        override_args: 原始覆盖参数列表

    Returns:
        list: 处理后的覆盖参数列表
    """
    processed = []

    for arg in override_args:
        # 支持 -o key=value 格式
        if "=" in arg:
            # 可能包含多个参数，用空格分隔
            if " " in arg:
                # 分割多个参数
                sub_args = arg.split()
                for sub_arg in sub_args:
                    if "=" in sub_arg:
                        processed.append(sub_arg)
                    else:
                        raise ValueError(f"覆盖参数格式错误: {sub_arg}，请使用 key=value 格式")
            else:
                processed.append(arg)
        else:
            # 支持 -o key value 格式（需要下一个参数作为值）
            # 这里简化处理，只支持 key=value 格式
            raise ValueError(f"覆盖参数格式错误: {arg}，请使用 key=value 格式")

    return processed


if __name__ == "__main__":
    # 测试配置解析
    config = parse_args()
    print("配置解析成功:")
    print(f"任务类型: {config.basic.task}")
    print(f"模型架构: {config.model.arch_name}")
    print(f"训练轮次: {config.training.epochs}")
    print(f"批次大小: {config.training.batch_size}")
