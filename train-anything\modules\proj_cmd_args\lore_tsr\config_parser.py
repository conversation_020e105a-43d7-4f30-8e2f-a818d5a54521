#!/usr/bin/env python3
"""
基于OmegaConf的LORE-TSR配置文件解析器

支持通过配置文件和命令行参数覆盖的方式进行配置管理。

Time: 2025-07-18
Author: LORE-TSR Migration Team
Description: 配置文件解析器，基于train-anything框架的OmegaConf系统

使用示例:
    python train_lore_tsr.py \
        --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml \
        -o training.epochs=100 \
           training.batch_size=16 \
           data.paths.train_data_dir=/path/to/data
"""

import os
import argparse
from pathlib import Path
from typing import List, Optional, Any
from omegaconf import OmegaConf, DictConfig


def parse_override_args(override_args: List[str]) -> DictConfig:
    """
    解析覆盖参数

    Args:
        override_args: 覆盖参数列表，格式为 ["key1=value1", "key2=value2"]
                      或者 ["key1=value1 key2=value2"] (支持空格分隔的多个参数)

    Returns:
        DictConfig: 解析后的配置对象
    """
    overrides = {}

    # 展开所有参数，支持空格分隔的多个key=value对
    all_args = []
    for arg in override_args:
        # 如果参数中包含空格，按空格分割
        if ' ' in arg and '=' in arg:
            # 分割并过滤空字符串
            split_args = [a.strip() for a in arg.split() if a.strip() and '=' in a.strip()]
            all_args.extend(split_args)
        else:
            all_args.append(arg)

    for arg in all_args:
        if '=' not in arg:
            raise ValueError(f"Invalid override format: {arg}. Expected format: key=value")

        key, value = arg.split('=', 1)

        # 处理嵌套键
        keys = key.split('.')
        current = overrides

        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]

        # 尝试自动类型转换
        final_key = keys[-1]
        try:
            # 尝试转换为数字
            if '.' in value:
                current[final_key] = float(value)
            else:
                current[final_key] = int(value)
        except ValueError:
            # 尝试转换为布尔值
            if value.lower() in ('true', 'false'):
                current[final_key] = value.lower() == 'true'
            elif value.lower() == 'null':
                current[final_key] = None
            else:
                # 保持为字符串
                current[final_key] = value

    return OmegaConf.create(overrides)


def load_config(config_path: str, overrides: Optional[List[str]] = None) -> DictConfig:
    """
    加载配置文件并应用覆盖参数
    
    Args:
        config_path: 配置文件路径
        overrides: 覆盖参数列表
        
    Returns:
        DictConfig: 最终的配置对象
    """
    # 检查配置文件是否存在
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Config file not found: {config_path}")
    
    # 加载基础配置
    base_config = OmegaConf.load(config_path)
    
    # 应用覆盖参数
    if overrides:
        override_config = parse_override_args(overrides)
        config = OmegaConf.merge(base_config, override_config)
    else:
        config = base_config
    
    return config


def validate_config(config: DictConfig) -> None:
    """
    验证LORE-TSR配置的有效性
    
    Args:
        config: 配置对象
    """
    # 检查必需的配置项
    required_fields = [
        "basic.task",
        "basic.output_dir",
        "model.arch_name",
        "training.epochs",
        "training.batch_size",
        "training.optimizer.learning_rate"
    ]
    
    for field in required_fields:
        try:
            value = OmegaConf.select(config, field)
            if value is None:
                raise ValueError(f"Required field '{field}' is not set")
        except Exception as e:
            raise ValueError(f"Error accessing field '{field}': {e}")
    
    # 验证输出目录可创建
    output_dir = config.basic.output_dir
    os.makedirs(output_dir, exist_ok=True)


if __name__ == "__main__":
    # 测试配置解析
    import sys
    if len(sys.argv) > 1:
        config_path = sys.argv[1]
        overrides = sys.argv[2:] if len(sys.argv) > 2 else None
        config = load_config(config_path, overrides)
        validate_config(config)
        print("Loaded LORE-TSR configuration:")
        print(OmegaConf.to_yaml(config))
    else:
        print("Usage: python config_parser.py <config_path> [override1=value1] [override2=value2] ...")
