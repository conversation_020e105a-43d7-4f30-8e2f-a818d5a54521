#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/7/9 14:36
# <AUTHOR> <EMAIL>
# @FileName: train_tools

import os
import re
import random
from pathlib import Path

import torch
import numpy as np


def get_optimizer(config, params_to_opt, optimizer_ckpt):
    """
    创建优化器，统一的优化器创建函数

    Args:
        config: 配置对象
        params_to_opt: 需要优化的参数
        optimizer_ckpt: 优化器检查点路径

    Returns:
        torch.optim.Optimizer: 创建的优化器
    """
    optimizer_config = config.training.optimizer

    if optimizer_config.type == "Adam":
        optimizer_class = torch.optim.Adam
    elif optimizer_config.type == "AdamW":
        optimizer_class = torch.optim.AdamW
    elif optimizer_config.type == "AdamW_8Bit":
        try:
            import bitsandbytes as bnb
        except ImportError:
            raise ImportError("To use 8-bit Adam, please install the bitsandbytes library: `pip install bitsandbytes`.")
        optimizer_class = bnb.optim.AdamW8bit
    elif optimizer_config.type == "SGD":
        optimizer_class = torch.optim.SGD
    else:
        raise TypeError("Unknown optimizer type: {}".format(optimizer_config.type))

    if "Adam" in optimizer_config.type:
        optimizer = optimizer_class(
            params_to_opt,
            lr=optimizer_config.learning_rate,
            betas=(optimizer_config.adamx.beta1, optimizer_config.adamx.beta2),
            weight_decay=optimizer_config.adamx.weight_decay,
            eps=optimizer_config.adamx.epsilon,
        )
    elif "SGD" in optimizer_config.type:
        optimizer = torch.optim.SGD(
            params_to_opt,
            lr=optimizer_config.learning_rate,
            momentum=optimizer_config.sgd.momentum,
            weight_decay=optimizer_config.sgd.weight_decay,
        )
    else:
        raise TypeError("Unknown optimizer type: {}".format(optimizer_config.type))

    if optimizer_ckpt is not None:
        optimizer_state_dict = torch.load(optimizer_ckpt, map_location='cpu')
        optimizer.load_state_dict(optimizer_state_dict)

    return optimizer


def get_random_states():
    """
    获取当前的随机状态

    Returns:
        dict: 包含各种随机状态的字典
    """
    return {
        "python_random": random.getstate(),
        "numpy_random": np.random.get_state(),
        "torch_random": torch.get_rng_state(),
        "torch_cuda_random": torch.cuda.get_rng_state_all() if torch.cuda.is_available() else None,
    }


def set_random_states(random_states):
    """
    设置随机状态

    Args:
        random_states: 随机状态字典
    """
    if "python_random" in random_states:
        random.setstate(random_states["python_random"])
    if "numpy_random" in random_states:
        np.random.set_state(random_states["numpy_random"])
    if "torch_random" in random_states:
        torch.set_rng_state(random_states["torch_random"])
    if "torch_cuda_random" in random_states and random_states["torch_cuda_random"] is not None:
        # 检查当前GPU数量与保存时的GPU数量是否匹配
        saved_cuda_states = random_states["torch_cuda_random"]
        current_gpu_count = torch.cuda.device_count()
        saved_gpu_count = len(saved_cuda_states)

        if current_gpu_count == saved_gpu_count:
            # GPU数量匹配，直接恢复所有状态
            torch.cuda.set_rng_state_all(saved_cuda_states)
        elif current_gpu_count > 0:
            # GPU数量不匹配，只恢复当前可用GPU数量范围内的状态
            print(f"Warning: GPU count mismatch. Saved: {saved_gpu_count}, Current: {current_gpu_count}")
            print("Only restoring CUDA random states for available GPUs.")

            # 只恢复当前可用GPU数量范围内的状态
            states_to_restore = saved_cuda_states[:current_gpu_count]
            if len(states_to_restore) > 0:
                torch.cuda.set_rng_state_all(states_to_restore)
        else:
            print("Warning: No CUDA devices available, skipping CUDA random state restoration.")


def manage_checkpoints(output_dir, n_checkpoints_to_keep, logger=None, file_logger=None):
    """
    管理检查点，删除旧的检查点

    Args:
        output_dir: 输出目录
        n_checkpoints_to_keep: 保留的检查点数量
        logger: 日志记录器
        file_logger: 文件日志记录器
    """
    checkpoints = sorted(
        [d for d in os.listdir(output_dir) if re.match(r'^checkpoint-\d+$', d)],
        key=lambda x: int(x.split('-')[1])
    )

    if len(checkpoints) > n_checkpoints_to_keep:
        print()
        checkpoints_to_delete = checkpoints[:-n_checkpoints_to_keep]
        msg = f"Old version checkpoints to delete: {checkpoints_to_delete}"
        if logger:
            logger.info(msg)
        if file_logger:
            file_logger.info(msg)

        for checkpoint in checkpoints_to_delete:
            checkpoint_path = os.path.join(output_dir, checkpoint)
            if os.path.isdir(checkpoint_path):
                for file in os.listdir(checkpoint_path):
                    file_path = os.path.join(checkpoint_path, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                os.rmdir(checkpoint_path)


def walk_dataloaders(loaders, weights=None, logger=None):
    """
    遍历多个数据加载器，以随机顺序返回每个加载器的批次。
    每个加载器可以根据权重比例进行动态调整。
    :param loaders: 多个数据加载器的列表，每个元素是 (标志, DataLoader)。
    :param weights: 各个加载器的采样权重，列表或 None。如果为 None，则根据加载器数据长度自动计算。
    :param logger: 日志记录器
    :yield: 返回 (标志, 批次)。
    """
    # 生成加载器的迭代器
    doing = [(flag, iter(loader), len(loader)) for flag, loader in loaders]

    # 如果未指定权重，根据数据加载器的长度计算权重
    if weights is None:
        total_length = sum(length for _, _, length in doing)
        weights = [length / total_length for _, _, length in doing]
    else:
        # 确保权重总和为 1
        weight_sum = sum(weights)
        weights = [w / weight_sum for w in weights]

    print()
    weight_with_flags = [(flag, weight) for (flag, _, _), weight in zip(doing, weights)]
    if logger:
        logger.info(f"walk dataloader by weights: {weight_with_flags}")

    # 确保权重的数量与加载器数量一致
    assert len(weights) == len(doing), "权重数量必须与加载器数量一致"

    while doing:
        # 按权重随机选择一个加载器
        selected_idx = random.choices(range(len(doing)), weights=weights, k=1)[0]
        flag, it, length = doing[selected_idx]

        try:
            batch = next(it)
            yield flag, batch
        except StopIteration:
            # 当前加载器完成，移除
            del doing[selected_idx]
            del weights[selected_idx]

            # 重新计算权重
            if doing:
                total_length = sum(length for _, _, length in doing)
                weights = [length / total_length for _, _, length in doing]


def walk_dataloaders_single_epoch(loaders):
    """
    遍历多个数据加载器，单个epoch版本（用于训练）
    当所有数据加载器都耗尽时停止（即一个epoch结束）

    Args:
        loaders: 数据加载器列表

    Yields:
        (loader_name, batch_data)
    """
    if not loaders:
        return

    # 创建迭代器
    iterators = [(name, iter(loader)) for name, loader in loaders]

    while iterators:
        # 随机选择一个数据加载器
        idx = random.randint(0, len(iterators) - 1)
        name, iterator = iterators[idx]

        try:
            batch = next(iterator)
            yield name, batch
        except StopIteration:
            # 该数据加载器已耗尽，移除它
            iterators.pop(idx)


def save_state(
    save_dir,
    model,
    optimizer,
    lr_scheduler,
    accelerator,
    n_checkpoints_to_keep=None,
    ema_handler=None,
    global_step=None,
    epoch=None,
    random_states=None,
    logger=None,
    file_logger=None,
):
    """
    保存训练状态，包含完整的训练进度信息

    Args:
        save_dir: 保存目录
        model: 模型
        optimizer: 优化器
        lr_scheduler: 学习率调度器
        accelerator: accelerate对象
        n_checkpoints_to_keep: 保留的检查点数量
        ema_handler: EMA处理器
        global_step: 全局步数
        epoch: 当前epoch
        random_states: 随机状态字典
        logger: 日志记录器
        file_logger: 文件日志记录器
    """
    os.makedirs(save_dir, exist_ok=True)

    model = accelerator.unwrap_model(model)
    optimizer = accelerator.unwrap_model(optimizer)
    lr_scheduler = accelerator.unwrap_model(lr_scheduler)

    torch.save(model.state_dict(), os.path.join(save_dir, "pytorch_model.bin"))
    torch.save(optimizer.state_dict(), os.path.join(save_dir, "optimizer.bin"))
    torch.save(lr_scheduler.state_dict(), os.path.join(save_dir, "scheduler.bin"))

    if ema_handler is not None:
        ema_handler.save(os.path.join(save_dir, "pytorch_model_ema.bin"))

    # 保存训练进度信息
    training_state = {
        "global_step": global_step,
        "epoch": epoch,
        "random_states": random_states or {},
    }
    torch.save(training_state, os.path.join(save_dir, "training_state.bin"))

    # 保持最新的n个checkpoint
    if n_checkpoints_to_keep is not None:
        manage_checkpoints(os.path.dirname(save_dir), n_checkpoints_to_keep, logger, file_logger)


def find_latest_checkpoint(output_dir, accelerator=None, logger=None, file_logger=None):
    """查找最新的checkpoint目录，支持checkpoint-*和model_epoch-*格式"""
    checkpoint_candidates = []

    # 查找checkpoint-*目录
    checkpoint_dirs = list(Path(output_dir).glob('checkpoint-*'))
    for ckpt_dir in checkpoint_dirs:
        try:
            step_num = int(ckpt_dir.stem.split('-')[1])
            checkpoint_candidates.append((step_num, str(ckpt_dir), 'checkpoint'))
        except (ValueError, IndexError):
            continue

    # 查找model_epoch-*目录
    epoch_dirs = list(Path(output_dir).glob('model_epoch-*'))
    for epoch_dir in epoch_dirs:
        try:
            # 从training_state.bin中读取实际的global_step
            training_state_path = os.path.join(epoch_dir, "training_state.bin")
            if os.path.exists(training_state_path):
                training_state = torch.load(training_state_path, map_location='cpu')
                step_num = training_state.get("global_step", 0)
                checkpoint_candidates.append((step_num, str(epoch_dir), 'model_epoch'))
        except (ValueError, IndexError, Exception):
            continue

    if not checkpoint_candidates:
        return None

    # 按步数排序，取最大的
    checkpoint_candidates.sort(key=lambda x: x[0], reverse=True)

    # 记录找到的checkpoint信息
    if accelerator and accelerator.is_main_process:
        msg = f"Found {len(checkpoint_candidates)} checkpoint candidates:"
        if logger:
            logger.info(msg)
        if file_logger:
            file_logger.info(msg)
        for step, path, ctype in checkpoint_candidates[:5]:  # 只显示前5个
            msg = f"  - {ctype}: step={step}, path={path}"
            if logger:
                logger.info(msg)
            if file_logger:
                file_logger.info(msg)

    return checkpoint_candidates[0]