# 表格结构识别训练可视化功能

## 功能概述

本模块为表格结构识别训练过程提供可视化功能，在验证阶段自动生成模型预测结果的可视化图片，用于监控训练效果和调试模型性能。

## 功能特性

- **自动可视化**：在每次验证时自动生成可视化结果
- **配置驱动**：通过配置文件控制可视化行为
- **多种展示**：支持边界框、关键点、中心点和热图可视化
- **批量处理**：支持多张图片的分组拼接保存
- **性能优化**：最小化对训练速度的影响

## 使用方法

### 1. 配置文件设置

在 `cycle_centernet_config.yaml` 中添加可视化配置：

```yaml
visualization:
  # 是否启用可视化功能
  enabled: true
  # 可视化样本图片路径
  sample_images_dir: "assets/vis4tsr"
  # 每次可视化的样本数量
  max_samples: 10
  # 可视化结果保存路径（null时默认为basic.output_dir/visualization_results）
  output_dir: null
  # 可视化频率：每N次验证进行一次可视化（1表示每次验证都可视化）
  frequency: 2
  
  # 可视化样式配置
  style:
    bbox_color: [0, 255, 0]        # 边界框颜色 (绿色)
    keypoint_color: [255, 0, 0]    # 关键点颜色 (红色)
    center_color: [255, 0, 0]      # 中心点颜色 (红色)
    transparency: 0.8              # 透明度
    line_thickness: 2              # 线条粗细
    point_radius: 4                # 点的半径
    
  # 热图可视化配置
  heatmap:
    colormap: "jet"                # 颜色映射
    normalize: true                # 是否归一化
    threshold: 0.1                 # 显示阈值
```

### 2. 准备可视化样本

在 `assets/vis4tsr/` 目录下放置用于可视化的图片文件：

```
assets/
└── vis4tsr/
    ├── sample1.jpg
    ├── sample2.png
    └── ...
```

支持的图片格式：`.jpg`, `.jpeg`, `.png`, `.bmp`, `.tiff`, `.tif`

### 3. 运行训练

正常运行训练脚本，可视化功能会自动集成到验证流程中：

```bash
python training_loops/table_structure_recognition/train_cycle_centernet.py \
    -c configs/table_structure_recognition/cycle_centernet/cycle_centernet_config.yaml
```

### 4. 查看可视化结果

可视化结果保存在输出目录的以下位置：

```
{output_dir}/
├── visualization_results/
│   ├── latest/                    # 最新的可视化结果
│   │   ├── visualization_latest_part1.png
│   │   └── ...
│   └── running_steps/             # 按训练步数保存的历史结果
│       ├── visualization_000000100_part1.png
│       ├── visualization_000000200_part1.png
│       └── ...
```

## 可视化内容说明

每个可视化图片包含两部分：

1. **左侧**：原图 + 预测结果叠加
   - 绿色边界框：预测的表格单元格边界
   - 红色圆点：预测的中心点和关键点

2. **右侧**：预测热图的伪彩色可视化
   - 使用jet颜色映射显示模型的置信度分布
   - 热点区域表示模型认为存在表格结构的位置

## 配置参数说明

### 基础配置

- `enabled`: 是否启用可视化功能
- `sample_images_dir`: 可视化样本图片目录路径
- `max_samples`: 每次可视化处理的最大样本数量
- `output_dir`: 可视化结果保存目录（设置为null时默认使用basic.output_dir/visualization_results）
- `frequency`: 可视化频率，每N次验证进行一次可视化（默认为1，即每次验证都可视化）

### 样式配置

- `bbox_color`: 边界框颜色，RGB格式
- `keypoint_color`: 关键点颜色，RGB格式
- `center_color`: 中心点颜色，RGB格式
- `transparency`: 绘制透明度，范围[0, 1]
- `line_thickness`: 线条粗细，像素值
- `point_radius`: 点的半径，像素值

### 热图配置

- `colormap`: matplotlib颜色映射名称（如"jet", "hot", "viridis"）
- `normalize`: 是否对热图进行归一化
- `threshold`: 热图显示阈值，低于此值的区域不显示

## 性能考虑

- 可视化过程在验证阶段执行，不影响训练速度
- 支持样本数量限制，避免处理过多图片
- 自动进行GPU内存清理，防止内存泄漏
- 异常处理机制确保可视化失败不影响训练流程

## 故障排除

### 常见问题

1. **可视化图片为空**
   - 检查 `sample_images_dir` 路径是否正确
   - 确认目录下有支持格式的图片文件

2. **可视化结果不正确**
   - 检查模型是否正确加载
   - 确认图片预处理参数与训练时一致

3. **内存不足**
   - 减少 `max_samples` 数量
   - 检查图片尺寸是否过大

### 调试模式

可以通过测试脚本验证功能：

```bash
python test_visualization.py
```

## 扩展功能

本模块设计支持后续扩展：

- 支持更多可视化样式和颜色配置
- 支持不同模型架构的可视化适配
- 支持可视化结果的定量分析
- 支持GPU加速的图像处理
