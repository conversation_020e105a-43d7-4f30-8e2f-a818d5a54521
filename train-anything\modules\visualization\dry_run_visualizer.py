#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-09 10:00
# <AUTHOR> <EMAIL>
# @FileName: dry_run_visualizer.py

"""
干运行模式专用可视化器

负责在干运行模式下将训练数据和目标的各个组件进行详细可视化，
包括预处理后的图像、热图目标、中心点、边界框、偏移向量等。
"""

import os
import torch
import numpy as np
from typing import Dict, Any, Tuple, List, Optional
from PIL import Image, ImageDraw
import matplotlib.pyplot as plt
import matplotlib.cm as cm

# 导入图像工具函数
from accelerate.logging import get_logger
import modules.utils.image_utils as base_image_utils
from modules.utils.image_utils import imagenet_denormalize_image

# 导入OmegaConf相关类型
from omegaconf import ListConfig

logger = get_logger(__name__)


class DryRunVisualizer:
    """
    干运行模式专用的数据可视化器
    
    负责将训练数据和目标的各个组件进行详细可视化，
    用于验证数据处理流程的正确性。
    """
    
    def __init__(self, config: Any):
        """
        初始化干运行可视化器
        
        Args:
            config: 配置对象，包含debug_visualization配置
        """
        self.config = config
        self.debug_vis_config = config.debug_visualization
        
        # 输出目录配置
        if self.debug_vis_config.dry_run_output_dir is not None:
            self.output_dir = self.debug_vis_config.dry_run_output_dir
        else:
            self.output_dir = os.path.join(config.basic.output_dir, "dry_run_results")
        
        # 样式配置
        self.style_config = self.debug_vis_config.style
        self.center_point_color = tuple(self.style_config.center_point_color)
        self.bbox_color = tuple(self.style_config.bbox_color)
        self.offset_color = tuple(self.style_config.offset_color)
        self.center2vertex_color = tuple(self.style_config.center2vertex_color)
        self.vertex2center_color = tuple(self.style_config.vertex2center_color)
        self.line_thickness = self.style_config.line_thickness
        self.point_radius = self.style_config.point_radius
        self.vector_scale = self.style_config.vector_scale
        
        # 数据处理配置
        self.mean = config.data.processing.normalize.mean
        self.std = config.data.processing.normalize.std

        # 热图配置
        heatmap_config = getattr(self.debug_vis_config, 'heatmap', {})
        self.colormap = getattr(heatmap_config, 'colormap', 'jet')
        self.normalize = getattr(heatmap_config, 'normalize', True)
        self.threshold = getattr(heatmap_config, 'threshold', 0.1)

        # 修复 ListConfig 类型问题：将 OmegaConf 的 ListConfig 转换为普通 Python 列表
        display_channel = getattr(heatmap_config, 'display_channel', [0, 1])
        if isinstance(display_channel, ListConfig):
            self.display_channel = list(display_channel)  # 转换为普通列表
        else:
            self.display_channel = display_channel  # 支持单通道或双通道

        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)

        logger.info(f"DryRunVisualizer初始化完成，输出目录: {self.output_dir}")
        logger.info(f"热图显示通道配置: {self.display_channel}")
    
    def visualize_batch(
        self, 
        batch_data: Dict[str, Any], 
        targets: Dict[str, torch.Tensor], 
        batch_idx: int
    ) -> None:
        """
        对单个批次的数据和目标进行完整可视化
        
        Args:
            batch_data: 包含图像、中心点、边界框等的批次数据字典
            targets: 包含热图、偏移、向量目标的目标字典
            batch_idx: 批次索引
        """
        logger.info(f"开始可视化批次 {batch_idx}")
        
        # 创建批次输出目录
        batch_dir = os.path.join(self.output_dir, f"batch_{batch_idx:04d}")
        os.makedirs(batch_dir, exist_ok=True)
        
        # 获取批次大小
        images = batch_data['images']
        batch_size = images.shape[0]
        
        # 遍历批次中的每个样本
        processed_images = []
        for sample_idx in range(batch_size):
            try:
                self._visualize_single_sample(
                    batch_data, targets, sample_idx, batch_dir, batch_idx
                )
                processed_images.append(f"sample_{sample_idx + 1:04d}")
            except Exception as e:
                logger.error(f"可视化样本 {sample_idx} 时出错: {e}")
                continue

        # 保存批次汇总结果
        self._save_batch_results(processed_images, batch_idx)

        logger.info(f"批次 {batch_idx} 可视化完成，保存到: {batch_dir}")
    
    def _denormalize_image(self, image_tensor: torch.Tensor) -> Image.Image:
        """
        将归一化后的图像张量还原为可显示的PIL图像

        Args:
            image_tensor: 归一化的图像张量 (C, H, W)

        Returns:
            PIL.Image 对象
        """
        # 使用 modules/utils/image_utils.py 中的标准反归一化函数
        return imagenet_denormalize_image(image_tensor)
    
    def _visualize_heatmap_target(self, heatmap_tensor: torch.Tensor, channel_name: str = "") -> Image.Image:
        """
        将热图目标可视化为伪彩色图像

        Args:
            heatmap_tensor: 热图张量 (H, W) 或 (1, H, W)
            channel_name: 通道名称，用于标识

        Returns:
            PIL.Image 热图可视化图像
        """
        # 处理张量维度
        if heatmap_tensor.dim() == 3:
            heatmap_tensor = heatmap_tensor.squeeze(0)

        # 转换为numpy数组
        heatmap = heatmap_tensor.detach().cpu().numpy()

        # 归一化到[0, 1]范围
        heatmap_min = heatmap.min()
        heatmap_max = heatmap.max()
        if heatmap_max > heatmap_min:
            heatmap = (heatmap - heatmap_min) / (heatmap_max - heatmap_min)
        else:
            heatmap = np.zeros_like(heatmap)

        # 应用colormap (修复matplotlib deprecation warning)
        try:
            colormap = plt.colormaps.get_cmap('jet')
        except AttributeError:
            colormap = cm.get_cmap('jet')
        heatmap_colored = colormap(heatmap)

        # 转换为PIL图像
        heatmap_image = Image.fromarray((heatmap_colored[:, :, :3] * 255).astype(np.uint8))

        return heatmap_image

    def _visualize_dual_channel_heatmap(self, heatmap_tensor: torch.Tensor) -> List[Image.Image]:
        """
        根据配置可视化热图通道

        Args:
            heatmap_tensor: 热图张量 (C, H, W)

        Returns:
            List[PIL.Image] 热图可视化图像列表
        """
        heatmap_images = []

        # 处理张量维度
        if heatmap_tensor.dim() == 4:
            heatmap_tensor = heatmap_tensor.squeeze(0)  # 移除batch维度

        # 解析 display_channel 配置
        if isinstance(self.display_channel, (list, ListConfig)):
            # 显示多个通道
            channels_to_show = self.display_channel
            # 确保 channels_to_show 是普通的 Python 列表
            if isinstance(channels_to_show, ListConfig):
                channels_to_show = list(channels_to_show)
        else:
            # 显示单个通道
            channels_to_show = [self.display_channel]

        # 通道名称映射
        channel_names = {0: "Center Points", 1: "Vertices"}

        # 生成指定通道的热图
        for channel_idx in channels_to_show:
            # 确保 channel_idx 是整数
            if isinstance(channel_idx, ListConfig):
                channel_idx = int(channel_idx)

            if channel_idx < heatmap_tensor.shape[0]:
                channel_tensor = heatmap_tensor[channel_idx]  # (H, W)
                channel_name = channel_names.get(channel_idx, f"Channel {channel_idx}")
                heatmap_image = self._visualize_heatmap_target(channel_tensor, channel_name)
                heatmap_images.append(heatmap_image)

        return heatmap_images
    
    def _visualize_center_points(
        self,
        image: Image.Image,
        centers: List[Tuple[float, float]],
        bboxes: Optional[List[Tuple[float, float, float, float]]] = None
    ) -> Image.Image:
        """
        在图像上可视化中心点和边界框

        Args:
            image: PIL图像对象
            centers: 中心点坐标列表
            bboxes: 边界框坐标列表 [(x1, y1, x2, y2), ...]

        Returns:
            绘制后的PIL图像
        """
        # 创建图像副本
        image_copy = image.copy()
        draw = ImageDraw.Draw(image_copy)

        # 绘制边界框
        if bboxes:
            for bbox in bboxes:
                if len(bbox) >= 4:
                    x1, y1, x2, y2 = bbox[:4]
                    # 绘制绿色边界框
                    draw.rectangle(
                        [x1, y1, x2, y2],
                        outline=self.bbox_color,
                        width=self.line_thickness
                    )

        # 绘制中心点
        for center in centers:
            cx, cy = int(center[0]), int(center[1])

            # 绘制中心点（红色圆点）
            draw.ellipse(
                [cx - self.point_radius, cy - self.point_radius,
                 cx + self.point_radius, cy + self.point_radius],
                fill=self.center_point_color,
                outline=self.center_point_color
            )

        return image_copy

    def _visualize_offset_target(
        self,
        image: Image.Image,
        offset_target: torch.Tensor,
        centers: List[Tuple[float, float]],
        scale_x: float = 1.0,
        scale_y: float = 1.0
    ) -> Image.Image:
        """
        可视化偏移目标向量

        Args:
            image: PIL图像对象
            offset_target: 偏移目标张量 (2, H, W)
            centers: 中心点坐标列表

        Returns:
            绘制偏移向量的PIL图像
        """
        # 创建图像副本
        image_copy = image.copy()
        draw = ImageDraw.Draw(image_copy)

        # 转换偏移目标为numpy数组
        offset_np = offset_target.detach().cpu().numpy()  # (2, H, W)

        # 为每个中心点绘制偏移向量
        for center in centers:
            cx, cy = int(center[0]), int(center[1])

            # 检查坐标是否在特征图范围内
            if 0 <= cy < offset_np.shape[1] and 0 <= cx < offset_np.shape[2]:
                # 获取该点的偏移向量（特征图坐标系）
                dx_feature = offset_np[0, cy, cx]
                dy_feature = offset_np[1, cy, cx]

                # 将偏移向量缩放到图像坐标系
                dx_image = dx_feature * scale_x * self.vector_scale
                dy_image = dy_feature * scale_y * self.vector_scale

                # 将中心点坐标缩放到图像坐标系
                cx_image = cx * scale_x
                cy_image = cy * scale_y

                # 计算终点坐标（图像坐标系）
                end_x = cx_image + dx_image
                end_y = cy_image + dy_image

                # 绘制蓝色偏移向量箭头
                self._draw_arrow(
                    draw, (cx_image, cy_image), (end_x, end_y),
                    color=self.offset_color, width=self.line_thickness
                )

        return image_copy

    def _draw_arrow(
        self,
        draw: Any,
        start: Tuple[float, float],
        end: Tuple[float, float],
        color: Tuple[int, int, int],
        width: int = 2,
        arrow_size: int = 8
    ) -> None:
        """
        绘制箭头

        Args:
            draw: ImageDraw对象
            start: 起点坐标
            end: 终点坐标
            color: 颜色
            width: 线条宽度
            arrow_size: 箭头大小
        """
        # 绘制主线
        draw.line([start, end], fill=color, width=width)

        # 计算箭头方向
        dx = end[0] - start[0]
        dy = end[1] - start[1]
        length = np.sqrt(dx*dx + dy*dy)

        if length > 0:
            # 单位向量
            ux = dx / length
            uy = dy / length

            # 箭头的两个边
            arrow_x1 = end[0] - arrow_size * ux + arrow_size * 0.5 * uy
            arrow_y1 = end[1] - arrow_size * uy - arrow_size * 0.5 * ux
            arrow_x2 = end[0] - arrow_size * ux - arrow_size * 0.5 * uy
            arrow_y2 = end[1] - arrow_size * uy + arrow_size * 0.5 * ux

            # 绘制箭头
            draw.line([end, (arrow_x1, arrow_y1)], fill=color, width=width)
            draw.line([end, (arrow_x2, arrow_y2)], fill=color, width=width)

    def _visualize_center2vertex_target(
        self,
        image: Image.Image,
        c2v_target: torch.Tensor,
        centers: List[Tuple[float, float]],
        scale_x: float = 1.0,
        scale_y: float = 1.0
    ) -> Image.Image:
        """
        可视化中心到顶点向量目标

        Args:
            image: PIL图像对象
            c2v_target: 中心到顶点目标张量 (8, H, W)
            centers: 中心点坐标列表

        Returns:
            绘制中心到顶点向量的PIL图像
        """
        # 创建图像副本
        image_copy = image.copy()
        draw = ImageDraw.Draw(image_copy)

        # 转换目标为numpy数组
        c2v_np = c2v_target.detach().cpu().numpy()  # (8, H, W)

        # 为每个中心点绘制4个中心到顶点的向量
        for center in centers:
            cx, cy = int(center[0]), int(center[1])

            # 检查坐标是否在特征图范围内
            if 0 <= cy < c2v_np.shape[1] and 0 <= cx < c2v_np.shape[2]:
                # 获取该点的8个向量值 (tl_x, tl_y, tr_x, tr_y, br_x, br_y, bl_x, bl_y)
                vectors = c2v_np[:, cy, cx]

                # 将中心点坐标缩放到图像坐标系
                cx_image = cx * scale_x
                cy_image = cy * scale_y

                # 检查是否有有效的向量值
                has_valid_vectors = any(abs(vectors[i]) > 0.01 for i in range(8))

                if has_valid_vectors:
                    # 固定颜色方案：4个顶点方向
                    vertex_colors = [
                        (255, 0, 0),    # 红色 - 左上角 (top-left)
                        (0, 255, 0),    # 绿色 - 右上角 (top-right)
                        (0, 0, 255),    # 蓝色 - 右下角 (bottom-right)
                        (255, 0, 255)   # 紫色 - 左下角 (bottom-left)
                    ]

                    vertex_names = ["TL", "TR", "BR", "BL"]

                    for i in range(0, 8, 2):
                        # 获取特征图坐标系的偏移量（像素距离）
                        dx_feature = vectors[i]      # x方向偏移量
                        dy_feature = vectors[i+1]    # y方向偏移量

                        # 只绘制有意义的向量（非零向量）
                        if abs(dx_feature) > 0.01 or abs(dy_feature) > 0.01:
                            # 将偏移量缩放到图像坐标系
                            # 这里不使用额外的vector_scale，直接按下采样比例缩放
                            dx_image = dx_feature * scale_x
                            dy_image = dy_feature * scale_y

                            # 计算顶点坐标（图像坐标系）
                            vertex_x = cx_image + dx_image
                            vertex_y = cy_image + dy_image

                            # 使用固定颜色
                            vertex_idx = i // 2
                            color = vertex_colors[vertex_idx]

                            # 添加调试信息
                            logger.debug(f"Center2vertex {vertex_names[vertex_idx]}: "
                                       f"feature_offset=({dx_feature:.2f}, {dy_feature:.2f}), "
                                       f"image_offset=({dx_image:.1f}, {dy_image:.1f})")

                            # 绘制从中心点到顶点的箭头
                            self._draw_arrow(
                                draw, (cx_image, cy_image), (vertex_x, vertex_y),
                                color=color, width=self.line_thickness
                            )

        return image_copy

    def _draw_dashed_arrow(
        self,
        draw: Any,
        start: Tuple[float, float],
        end: Tuple[float, float],
        color: Tuple[int, int, int],
        width: int = 2,
        dash_length: int = 5,
        arrow_size: int = 8
    ) -> None:
        """
        绘制虚线箭头

        Args:
            draw: ImageDraw对象
            start: 起点坐标
            end: 终点坐标
            color: 颜色
            width: 线条宽度
            dash_length: 虚线长度
            arrow_size: 箭头大小
        """
        # 计算线段长度和方向
        dx = end[0] - start[0]
        dy = end[1] - start[1]
        length = np.sqrt(dx*dx + dy*dy)

        if length > 0:
            # 单位向量
            ux = dx / length
            uy = dy / length

            # 绘制虚线
            current_pos = 0
            while current_pos < length:
                # 计算当前段的起点和终点
                seg_start_x = start[0] + current_pos * ux
                seg_start_y = start[1] + current_pos * uy
                seg_end_pos = min(current_pos + dash_length, length)
                seg_end_x = start[0] + seg_end_pos * ux
                seg_end_y = start[1] + seg_end_pos * uy

                # 绘制线段
                draw.line(
                    [(seg_start_x, seg_start_y), (seg_end_x, seg_end_y)],
                    fill=color, width=width
                )

                # 跳过间隔
                current_pos += dash_length * 2

            # 绘制箭头
            arrow_x1 = end[0] - arrow_size * ux + arrow_size * 0.5 * uy
            arrow_y1 = end[1] - arrow_size * uy - arrow_size * 0.5 * ux
            arrow_x2 = end[0] - arrow_size * ux - arrow_size * 0.5 * uy
            arrow_y2 = end[1] - arrow_size * uy + arrow_size * 0.5 * ux

            draw.line([end, (arrow_x1, arrow_y1)], fill=color, width=width)
            draw.line([end, (arrow_x2, arrow_y2)], fill=color, width=width)

    def _visualize_vertex2center_target(
        self,
        image: Image.Image,
        v2c_target: torch.Tensor,
        centers: List[Tuple[float, float]],
        scale_x: float = 1.0,
        scale_y: float = 1.0
    ) -> Image.Image:
        """
        可视化顶点到中心向量目标

        Args:
            image: PIL图像对象
            v2c_target: 顶点到中心目标张量 (8, H, W)
            centers: 中心点坐标列表

        Returns:
            绘制顶点到中心向量的PIL图像
        """
        # 创建图像副本
        image_copy = image.copy()
        draw = ImageDraw.Draw(image_copy)

        # 转换目标为numpy数组
        v2c_np = v2c_target.detach().cpu().numpy()  # (8, H, W)

        # 为每个中心点绘制4个顶点到中心的向量
        # 注意：vertex2center的向量存储在顶点位置，而不是中心点位置

        # 遍历特征图上的所有位置，寻找非零的vertex2center向量
        for y in range(v2c_np.shape[1]):
            for x in range(v2c_np.shape[2]):
                # 获取该位置的8个向量值 (tl_x, tl_y, tr_x, tr_y, br_x, br_y, bl_x, bl_y)
                vectors = v2c_np[:, y, x]

                # 检查是否有有效的向量值
                has_valid_vectors = any(abs(vectors[i]) > 0.01 for i in range(8))

                if has_valid_vectors:
                    # 当前位置就是顶点位置（特征图坐标系）
                    vertex_x_feature = x
                    vertex_y_feature = y

                    # 将顶点坐标缩放到图像坐标系
                    vertex_x_image = vertex_x_feature * scale_x
                    vertex_y_image = vertex_y_feature * scale_y

                    # 固定颜色方案：4个顶点方向（与center2vertex保持一致但颜色不同）
                    vertex_colors = [
                        (255, 255, 0),  # 黄色 - 左上角 (top-left)
                        (0, 255, 255),  # 青色 - 右上角 (top-right)
                        (255, 128, 0),  # 橙色 - 右下角 (bottom-right)
                        (128, 0, 255)   # 紫色 - 左下角 (bottom-left)
                    ]

                    vertex_names = ["TL", "TR", "BR", "BL"]

                    for i in range(0, 8, 2):
                        # 获取特征图坐标系的偏移量（从顶点到中心的像素距离）
                        dx_feature = vectors[i]      # x方向偏移量
                        dy_feature = vectors[i+1]    # y方向偏移量

                        # 只绘制有意义的向量（非零向量）
                        if abs(dx_feature) > 0.01 or abs(dy_feature) > 0.01:
                            # 将偏移量缩放到图像坐标系
                            dx_image = dx_feature * scale_x
                            dy_image = dy_feature * scale_y

                            # 计算中心点坐标（图像坐标系）
                            center_x = vertex_x_image + dx_image
                            center_y = vertex_y_image + dy_image

                            # 使用固定颜色
                            vertex_idx = i // 2
                            color = vertex_colors[vertex_idx]

                            # 添加调试信息
                            logger.debug(f"Vertex2center {vertex_names[vertex_idx]} at ({vertex_x_feature}, {vertex_y_feature}): "
                                       f"feature_offset=({dx_feature:.2f}, {dy_feature:.2f}), "
                                       f"image_offset=({dx_image:.1f}, {dy_image:.1f})")

                            # 绘制从顶点到中心的箭头
                            self._draw_arrow(
                                draw, (vertex_x_image, vertex_y_image), (center_x, center_y),
                                color=color, width=self.line_thickness
                            )

        return image_copy

    def _visualize_single_sample(
        self,
        batch_data: Dict[str, Any],
        targets: Dict[str, torch.Tensor],
        sample_idx: int,
        batch_dir: str,
        batch_idx: int
    ) -> None:
        """
        可视化单个样本的所有组件

        Args:
            batch_data: 批次数据
            targets: 目标数据
            sample_idx: 样本索引
            batch_dir: 批次输出目录
            batch_idx: 批次索引
        """
        sample_name = f"sample_{sample_idx + 1:04d}"

        # 1. 逆向归一化处理图像
        image_tensor = batch_data['images'][sample_idx]  # (C, H, W)
        processed_image = self._denormalize_image(image_tensor)
        processed_image.save(os.path.join(batch_dir, f"{sample_name}_processed.png"))

        # 2. 可视化热图目标（支持双通道）
        if 'heatmap_target' in targets:
            heatmap_tensor = targets['heatmap_target'][sample_idx]  # (C, H, W)
            heatmap_images = self._visualize_dual_channel_heatmap(heatmap_tensor)

            # 根据配置保存热图
            if len(heatmap_images) == 1:
                # 单通道热图
                if isinstance(self.display_channel, list):
                    # 如果配置是列表但只有一个通道，使用通道名
                    channel_idx = self.display_channel[0]
                    # 确保 channel_idx 是整数
                    if isinstance(channel_idx, ListConfig):
                        channel_idx = int(channel_idx)
                    channel_name = "center_points" if channel_idx == 0 else "vertices"
                    heatmap_images[0].save(os.path.join(batch_dir, f"{sample_name}_heatmap_{channel_name}.png"))
                else:
                    # 单通道配置
                    display_channel = self.display_channel
                    if isinstance(display_channel, ListConfig):
                        display_channel = int(display_channel)
                    channel_name = "center_points" if display_channel == 0 else "vertices"
                    heatmap_images[0].save(os.path.join(batch_dir, f"{sample_name}_heatmap_{channel_name}.png"))
            else:
                # 多通道热图
                channels_to_show = self.display_channel if isinstance(self.display_channel, list) else [self.display_channel]
                # 确保 channels_to_show 是普通的 Python 列表
                if isinstance(channels_to_show, ListConfig):
                    channels_to_show = list(channels_to_show)
                for i, heatmap_image in enumerate(heatmap_images):
                    channel_idx = channels_to_show[i]
                    # 确保 channel_idx 是整数
                    if isinstance(channel_idx, ListConfig):
                        channel_idx = int(channel_idx)
                    channel_name = "center_points" if channel_idx == 0 else "vertices"
                    heatmap_image.save(os.path.join(batch_dir, f"{sample_name}_heatmap_{channel_name}.png"))

        # 3. 获取中心点和边界框信息
        image_centers = []
        image_bboxes = []

        # 处理中心点
        if 'cell_centers' in batch_data:
            centers = batch_data['cell_centers'][sample_idx]
            if isinstance(centers, torch.Tensor):
                centers = centers.cpu().numpy().tolist()

            # 将中心点坐标转换为图像坐标系
            for center in centers:
                if len(center) >= 2:
                    # 假设中心点坐标已经是图像坐标系
                    image_centers.append((center[0], center[1]))

        # 处理边界框
        if 'bboxes' in batch_data:
            bboxes = batch_data['bboxes'][sample_idx]
            if isinstance(bboxes, torch.Tensor):
                bboxes = bboxes.cpu().numpy().tolist()

            for bbox in bboxes:
                if len(bbox) >= 4:
                    image_bboxes.append((bbox[0], bbox[1], bbox[2], bbox[3]))

        # 可视化中心点和边界框（如果有的话）
        if image_centers or image_bboxes:
            centers_image = self._visualize_center_points(
                processed_image, image_centers, image_bboxes if image_bboxes else None
            )
            centers_image.save(os.path.join(batch_dir, f"{sample_name}_centers_bbox.png"))

        # 4. 可视化偏移目标（无论是否有中心点都尝试可视化）
        if 'offset_target' in targets:
            offset_tensor = targets['offset_target'][sample_idx]  # (2, H, W)
            if image_centers:
                # 计算坐标缩放比例（从特征图坐标系到图像坐标系）
                image_h, image_w = processed_image.size[1], processed_image.size[0]  # PIL图像的size是(width, height)
                feature_h, feature_w = offset_tensor.shape[1], offset_tensor.shape[2]
                scale_x = image_w / feature_w
                scale_y = image_h / feature_h

                # 将中心点坐标缩放到特征图坐标系
                feature_centers = [(cx / scale_x, cy / scale_y) for cx, cy in image_centers]

                # 如果有中心点，在中心点位置绘制偏移向量
                offset_image = self._visualize_offset_target(processed_image, offset_tensor, feature_centers, scale_x, scale_y)
            else:
                # 如果没有中心点，创建一个空的可视化图像
                offset_image = processed_image.copy()
                logger.warning(f"样本 {sample_name} 没有中心点，跳过偏移向量可视化")
            offset_image.save(os.path.join(batch_dir, f"{sample_name}_offset.png"))

        # 5. 可视化中心到顶点向量
        if 'center2vertex_target' in targets:
            c2v_tensor = targets['center2vertex_target'][sample_idx]  # (8, H, W)
            if image_centers:
                # 计算坐标缩放比例（从特征图坐标系到图像坐标系）
                image_h, image_w = processed_image.size[1], processed_image.size[0]  # PIL图像的size是(width, height)
                feature_h, feature_w = c2v_tensor.shape[1], c2v_tensor.shape[2]
                scale_x = image_w / feature_w
                scale_y = image_h / feature_h

                # 将中心点坐标缩放到特征图坐标系
                feature_centers = [(cx / scale_x, cy / scale_y) for cx, cy in image_centers]

                c2v_image = self._visualize_center2vertex_target(processed_image, c2v_tensor, feature_centers, scale_x, scale_y)
            else:
                c2v_image = processed_image.copy()
                logger.warning(f"样本 {sample_name} 没有中心点，跳过中心到顶点向量可视化")
            c2v_image.save(os.path.join(batch_dir, f"{sample_name}_center2vertex.png"))

        # 6. 可视化顶点到中心向量
        if 'vertex2center_target' in targets:
            v2c_tensor = targets['vertex2center_target'][sample_idx]  # (8, H, W)
            if image_centers:
                # 计算坐标缩放比例（从特征图坐标系到图像坐标系）
                image_h, image_w = processed_image.size[1], processed_image.size[0]  # PIL图像的size是(width, height)
                feature_h, feature_w = v2c_tensor.shape[1], v2c_tensor.shape[2]
                scale_x = image_w / feature_w
                scale_y = image_h / feature_h

                # 将中心点坐标缩放到特征图坐标系
                feature_centers = [(cx / scale_x, cy / scale_y) for cx, cy in image_centers]

                v2c_image = self._visualize_vertex2center_target(processed_image, v2c_tensor, feature_centers, scale_x, scale_y)
            else:
                v2c_image = processed_image.copy()
                logger.warning(f"样本 {sample_name} 没有中心点，跳过顶点到中心向量可视化")
            v2c_image.save(os.path.join(batch_dir, f"{sample_name}_vertex2center.png"))

        logger.debug(f"样本 {sample_name} 可视化完成")

    def _save_batch_results(self, processed_samples: List[str], batch_idx: int) -> None:
        """
        保存批次可视化结果

        Args:
            processed_samples: 已处理的样本名称列表
            batch_idx: 批次索引
        """
        if not processed_samples:
            return

        # 创建批次汇总信息
        batch_dir = os.path.join(self.output_dir, f"batch_{batch_idx:04d}")
        summary_file = os.path.join(batch_dir, "batch_summary.txt")

        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"批次 {batch_idx} 可视化汇总\n")
            f.write(f"=" * 50 + "\n")
            f.write(f"样本数量: {len(processed_samples)}\n")
            f.write(f"输出目录: {batch_dir}\n")
            f.write(f"生成时间: {os.path.getctime(batch_dir)}\n")
            f.write("\n可视化文件列表:\n")

            # 列出所有生成的文件
            for file_name in sorted(os.listdir(batch_dir)):
                if file_name.endswith('.png'):
                    f.write(f"  - {file_name}\n")

        logger.info(f"批次 {batch_idx} 汇总信息已保存到: {summary_file}")
