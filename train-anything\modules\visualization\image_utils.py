#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-08 16:00
# <AUTHOR> <EMAIL>
# @FileName: image_utils.py

"""
图像处理工具函数

提供表格结构可视化所需的图像加载、预处理、绘制和保存功能。
"""

import os
import cv2
import torch
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.cm as cm
from pathlib import Path
from typing import List, Tuple, Union, Optional, Dict, Any
from PIL import Image, ImageDraw
import warnings

import modules.utils.image_utils as base_image_utils

# 设置PIL图像大小限制，避免DecompressionBombWarning
Image.MAX_IMAGE_PIXELS = None


def load_and_preprocess_image(
    image_path: Union[str, Path],
    target_size: Tuple[int, int],
    mean: List[float] = [0.485, 0.456, 0.406],
    std: List[float] = [0.229, 0.224, 0.225],
    to_rgb: bool = True
) -> Tuple[Image.Image, torch.Tensor]:
    """
    加载图片并进行预处理，转换为模型输入格式
    
    Args:
        image_path: 图片文件路径
        target_size: 目标尺寸(height, width)
        mean: 归一化均值
        std: 归一化标准差
        to_rgb: 是否转换为RGB格式
        
    Returns:
        (original_image: PIL.Image, preprocessed_tensor: torch.Tensor)
    """
    # 加载原始图像
    image = Image.open(image_path)
    if to_rgb:
        image = image.convert('RGB')
    
    # 保存原始图像用于可视化
    original_image = image.copy()
    
    # 调整尺寸
    image = image.resize((target_size[1], target_size[0]), Image.Resampling.BILINEAR)
    
    # 转换为张量并归一化
    image_array = np.array(image).astype(np.float32) / 255.0
    
    # 应用归一化
    for i in range(3):
        image_array[:, :, i] = (image_array[:, :, i] - mean[i]) / std[i]
    
    # 转换为PyTorch张量 (C, H, W)
    image_tensor = torch.from_numpy(image_array).permute(2, 0, 1)
    
    return original_image, image_tensor


def draw_predictions_on_image(
    image: Image.Image,
    predictions: Dict[str, Any],
    transparency: float = 0.8,
    bbox_color: Tuple[int, int, int] = (0, 255, 0),
    keypoint_color: Tuple[int, int, int] = (255, 0, 0),
    center_color: Tuple[int, int, int] = (255, 0, 0),
    line_thickness: int = 2,
    point_radius: int = 4
) -> Image.Image:
    """
    在原图上绘制预测结果（边界框、关键点、中心点）
    
    Args:
        image: PIL图像对象
        predictions: 模型预测结果字典
        transparency: 绘制透明度
        bbox_color: 边界框颜色 (R, G, B)
        keypoint_color: 关键点颜色 (R, G, B)
        center_color: 中心点颜色 (R, G, B)
        line_thickness: 线条粗细
        point_radius: 点的半径
        
    Returns:
        绘制后的PIL图像
    """
    # 创建图像副本
    result_image = image.copy()
    draw = ImageDraw.Draw(result_image)
    
    # 获取图像尺寸
    img_width, img_height = image.size
    
    # 绘制多边形（优先使用多边形格式）
    if 'polygons' in predictions and len(predictions['polygons']) > 0:
        polygons = predictions['polygons']
        for poly in polygons:
            if len(poly) >= 8:  # 确保有足够的坐标点
                # 多边形格式: [x1, y1, x2, y2, x3, y3, x4, y4]
                points = []
                for i in range(0, len(poly), 2):
                    x, y = poly[i], poly[i+1]

                    # 检查坐标是否为相对坐标（0-1范围）还是绝对坐标
                    if x <= 1.0 and y <= 1.0:
                        # 相对坐标，需要转换为图像坐标
                        x = int(x * img_width)
                        y = int(y * img_height)
                    else:
                        # 绝对坐标，直接使用
                        x = int(x)
                        y = int(y)

                    points.append((x, y))

                # 绘制多边形
                if len(points) >= 3:  # 至少需要3个点才能构成多边形
                    draw.polygon(points, outline=bbox_color, width=line_thickness)

    # 如果没有多边形，则绘制边界框
    elif 'bboxes' in predictions and len(predictions['bboxes']) > 0:
        bboxes = predictions['bboxes']
        for bbox in bboxes:
            # bbox格式: [x_min, y_min, x_max, y_max]
            x_min, y_min, x_max, y_max = bbox

            # 检查坐标是否为相对坐标（0-1范围）还是绝对坐标
            if x_max <= 1.0 and y_max <= 1.0:
                # 相对坐标，需要转换为图像坐标
                x_min = int(x_min * img_width)
                y_min = int(y_min * img_height)
                x_max = int(x_max * img_width)
                y_max = int(y_max * img_height)
            else:
                # 绝对坐标，直接使用
                x_min = int(x_min)
                y_min = int(y_min)
                x_max = int(x_max)
                y_max = int(y_max)

            # 绘制矩形边界框
            draw.rectangle(
                [(x_min, y_min), (x_max, y_max)],
                outline=bbox_color,
                width=line_thickness
            )
    
    # 绘制关键点
    if 'keypoints' in predictions:
        keypoints = predictions['keypoints']
        for keypoint in keypoints:
            x, y = keypoint

            # 检查坐标是否为相对坐标还是绝对坐标
            if x <= 1.0 and y <= 1.0:
                # 相对坐标，转换为图像坐标
                x = int(x * img_width)
                y = int(y * img_height)
            else:
                # 绝对坐标，直接使用
                x = int(x)
                y = int(y)

            # 绘制圆形关键点
            draw.ellipse(
                [(x - point_radius, y - point_radius),
                 (x + point_radius, y + point_radius)],
                fill=keypoint_color,
                outline=keypoint_color
            )

    # 绘制中心点
    if 'center_points' in predictions:
        center_points = predictions['center_points']
        for center in center_points:
            x, y = center

            # 检查坐标是否为相对坐标还是绝对坐标
            if x <= 1.0 and y <= 1.0:
                # 相对坐标，转换为图像坐标
                x = int(x * img_width)
                y = int(y * img_height)
            else:
                # 绝对坐标，直接使用
                x = int(x)
                y = int(y)

            # 绘制圆形中心点
            draw.ellipse(
                [(x - point_radius, y - point_radius),
                 (x + point_radius, y + point_radius)],
                fill=center_color,
                outline=center_color
            )
    
    return result_image


def create_heatmap_visualization(
    heatmap_tensor: torch.Tensor,
    colormap: str = "jet",
    normalize: bool = True,
    threshold: float = 0.1
) -> Image.Image:
    """
    将热图张量转换为伪彩色可视化图像
    
    Args:
        heatmap_tensor: 热图张量 (H, W) 或 (1, H, W)
        colormap: 颜色映射名称
        normalize: 是否归一化
        threshold: 显示阈值
        
    Returns:
        热图可视化PIL图像
    """
    # 处理张量维度
    if heatmap_tensor.dim() == 3:
        heatmap_tensor = heatmap_tensor.squeeze(0)
    
    # 转换为numpy数组
    heatmap = heatmap_tensor.detach().cpu().numpy()
    
    # 归一化到[0, 1]范围
    if normalize:
        heatmap_min = heatmap.min()
        heatmap_max = heatmap.max()
        if heatmap_max > heatmap_min:
            heatmap = (heatmap - heatmap_min) / (heatmap_max - heatmap_min)
        else:
            heatmap = np.zeros_like(heatmap)
    
    # 应用阈值
    heatmap[heatmap < threshold] = 0
    
    # 应用颜色映射
    cmap = cm.get_cmap(colormap)
    colored_heatmap = cmap(heatmap)
    
    # 转换为PIL图像 (移除alpha通道)
    colored_heatmap_rgb = (colored_heatmap[:, :, :3] * 255).astype(np.uint8)
    heatmap_image = Image.fromarray(colored_heatmap_rgb)
    
    return heatmap_image


def combine_images_horizontally(
    image1: Image.Image,
    image2: Image.Image,
    resize_to_match: bool = True
) -> Image.Image:
    """
    将两张图片水平拼接
    
    Args:
        image1: 第一张图片
        image2: 第二张图片
        resize_to_match: 是否调整尺寸匹配
        
    Returns:
        拼接后的PIL图像
    """
    if resize_to_match:
        # 调整第二张图片的尺寸匹配第一张图片的高度
        height = image1.height
        width2 = int(image2.width * height / image2.height)
        image2 = image2.resize((width2, height), Image.Resampling.BILINEAR)
    
    # 创建新的图像
    total_width = image1.width + image2.width
    max_height = max(image1.height, image2.height)
    
    combined_image = Image.new('RGB', (total_width, max_height), (255, 255, 255))
    
    # 粘贴图像
    combined_image.paste(image1, (0, 0))
    combined_image.paste(image2, (image1.width, 0))
    
    return combined_image


def save_grouped_images(
    images: List[np.ndarray],
    latest_dir: str,
    running_steps_dir: str,
    global_step: int,
    group_size: int = 10
) -> None:
    """
    将图像分组，每组拼接成一张大图，并保存到指定目录
    修复版本：处理不同尺寸图像的拼接问题

    Args:
        images: 图像列表
        latest_dir: 最新结果保存目录
        running_steps_dir: 按步数保存的目录
        global_step: 全局步数
        group_size: 每组图像数量
    """
    if not images:
        return

    # 确保目录存在
    os.makedirs(latest_dir, exist_ok=True)
    os.makedirs(running_steps_dir, exist_ok=True)

    total_parts = (len(images) + group_size - 1) // group_size  # 计算总组数

    for part_idx in range(total_parts):
        # 获取当前组的图像
        start_idx = part_idx * group_size
        end_idx = min(start_idx + group_size, len(images))
        grouped_images = images[start_idx:end_idx]

        if not grouped_images:
            continue

        try:
            # 统一图像宽度：找到最大宽度
            max_width = max(img.shape[1] for img in grouped_images)

            # 调整所有图像到相同宽度（居中对齐）
            aligned_images = []
            for img in grouped_images:
                if img.shape[1] < max_width:
                    # 计算需要填充的宽度
                    pad_width = max_width - img.shape[1]
                    left_pad = pad_width // 2
                    right_pad = pad_width - left_pad

                    # 使用白色填充
                    if len(img.shape) == 3:
                        # 彩色图像
                        padded_img = np.pad(img, ((0, 0), (left_pad, right_pad), (0, 0)),
                                            mode='constant', constant_values=0)
                    else:
                        # 灰度图像
                        padded_img = np.pad(img, ((0, 0), (left_pad, right_pad)),
                                            mode='constant', constant_values=0)
                    aligned_images.append(padded_img)
                else:
                    aligned_images.append(img)

            # 将组内图像拼接成一张大图
            grouped_image = np.concatenate(aligned_images, axis=0)
            grouped_image = Image.fromarray(grouped_image)

            # 生成带后缀的文件名
            part_suffix = f"part{part_idx + 1}"
            latest_file_name = f"visualization_latest_{part_suffix}.png"
            running_steps_file_name = f"visualization_{global_step:09d}_{part_suffix}.png"

            # 保存图像
            grouped_image.save(os.path.join(latest_dir, latest_file_name))
            grouped_image.save(os.path.join(running_steps_dir, running_steps_file_name))

        except Exception as e:
            # 如果拼接失败，单独保存每张图像
            warnings.warn(f"图像拼接失败，将单独保存: {e}")
            for i, img in enumerate(grouped_images):
                try:
                    img_pil = Image.fromarray(img)
                    part_suffix = f"part{part_idx + 1}_img{i + 1}"
                    latest_file_name = f"visualization_latest_{part_suffix}.png"
                    running_steps_file_name = f"visualization_{global_step:09d}_{part_suffix}.png"

                    img_pil.save(os.path.join(latest_dir, latest_file_name))
                    img_pil.save(os.path.join(running_steps_dir, running_steps_file_name))
                except Exception as save_e:
                    warnings.warn(f"保存单张图像失败: {save_e}")
