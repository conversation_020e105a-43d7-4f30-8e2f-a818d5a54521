#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-08 16:00
# <AUTHOR> <EMAIL>
# @FileName: table_structure_visualizer.py

"""
表格结构可视化核心模块

负责表格结构识别模型预测结果的可视化，包括模型推理、结果绘制和图片保存的完整流程。
"""

import os
import torch
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

from PIL import Image
from tqdm import tqdm

from modules.utils.log import LOGGER
from .image_utils import (
    load_and_preprocess_image,
    draw_predictions_on_image,
    create_heatmap_visualization,
    combine_images_horizontally,
    save_grouped_images
)

logger = LOGGER


class TableStructureVisualizer:
    """
    表格结构可视化器
    
    负责在验证阶段生成模型预测结果的可视化图片，用于监控训练效果和调试模型性能。
    """
    
    def __init__(
        self,
        config: Any,
        device: torch.device,
        weight_dtype: torch.dtype
    ):
        """
        初始化可视化器
        
        Args:
            config: 完整配置对象，包含visualization配置节
            device: 推理设备（GPU/CPU）
            weight_dtype: 模型权重数据类型
        """
        self.config = config
        self.device = device
        self.weight_dtype = weight_dtype
        
        # 可视化配置
        self.vis_config = getattr(config, 'visualization', None)
        if self.vis_config is None:
            raise ValueError("配置文件中缺少visualization配置节")
        
        self.enabled = getattr(self.vis_config, 'enabled', False)
        self.sample_images_dir = getattr(self.vis_config, 'sample_images_dir', 'assets/vis4tsr')
        self.max_samples = getattr(self.vis_config, 'max_samples', 10)
        self.frequency = getattr(self.vis_config, 'frequency', 1)  # 可视化频率

        # 处理output_dir配置：如果为null，则使用basic.output_dir/visualization_results
        vis_output_dir = getattr(self.vis_config, 'output_dir', None)
        if vis_output_dir is None:
            self.output_dir = 'visualization_results'  # 相对于basic.output_dir的路径
        else:
            self.output_dir = vis_output_dir

        # 可视化计数器，用于控制频率
        self.visualization_counter = 0
        
        # 样式配置（使用默认值）
        style_config = getattr(self.vis_config, 'style', {})
        self.bbox_color = getattr(style_config, 'bbox_color', [0, 255, 0])
        self.keypoint_color = getattr(style_config, 'keypoint_color', [255, 0, 0])
        self.center_color = getattr(style_config, 'center_color', [255, 0, 0])
        self.transparency = getattr(style_config, 'transparency', 0.8)
        self.line_thickness = getattr(style_config, 'line_thickness', 2)
        self.point_radius = getattr(style_config, 'point_radius', 4)
        
        # 热图配置
        heatmap_config = getattr(self.vis_config, 'heatmap', {})
        self.colormap = getattr(heatmap_config, 'colormap', 'jet')
        self.normalize = getattr(heatmap_config, 'normalize', True)
        self.threshold = getattr(heatmap_config, 'threshold', 0.1)
        
        # 数据处理配置
        self.target_size = tuple(config.data.processing.image_size)
        self.mean = config.data.processing.normalize.mean
        self.std = config.data.processing.normalize.std
        self.to_rgb = config.data.processing.normalize.to_rgb
        
        # 创建输出目录结构
        self._setup_output_directories()
        
        logger.info(f"TableStructureVisualizer初始化完成，启用状态: {self.enabled}")
    
    def _setup_output_directories(self) -> None:
        """创建输出目录结构"""
        if not self.enabled:
            return
        
        base_output_dir = os.path.join(self.config.basic.output_dir, self.output_dir)
        self.latest_dir = os.path.join(base_output_dir, 'latest')
        self.running_steps_dir = os.path.join(base_output_dir, 'running_steps')
        
        os.makedirs(self.latest_dir, exist_ok=True)
        os.makedirs(self.running_steps_dir, exist_ok=True)
    
    def visualize_validation_samples(
        self,
        model: torch.nn.Module,
        global_step: int,
        accelerator: Any
    ) -> None:
        """
        主要的可视化入口函数，协调整个可视化流程
        
        Args:
            model: 训练好的CycleCenterNet模型
            global_step: 当前训练步数
            accelerator: accelerate框架对象
        """
        if not self.enabled:
            logger.info("可视化功能未启用，跳过可视化")
            return

        # 检查可视化频率
        self.visualization_counter += 1
        if self.visualization_counter % self.frequency != 0:
            logger.info(f"跳过可视化（频率控制：{self.visualization_counter}/{self.frequency}）")
            return

        logger.info(f"开始生成步数 {global_step} 的可视化结果（第 {self.visualization_counter} 次验证）")
        
        # 准备可视化样本
        image_paths = self.prepare_visualization_samples()
        if not image_paths:
            logger.warning("未找到可视化样本图片")
            return
        
        # 处理每个样本
        images_to_save = []
        vis_pbar = tqdm(total=len(image_paths), desc='生成可视化结果...')

        # 设置统一的输出尺寸，避免拼接时尺寸不匹配
        target_width = 1920  # 统一宽度

        for image_path in image_paths:
            try:
                combined_image = self.process_single_sample(image_path, model)
                if combined_image is not None:
                    # 调整图像尺寸到统一宽度
                    if combined_image.width != target_width:
                        # 按比例调整高度
                        target_height = int(combined_image.height * target_width / combined_image.width)
                        combined_image = combined_image.resize((target_width, target_height), Image.Resampling.LANCZOS)

                    # 转换为numpy数组用于保存
                    combined_array = np.array(combined_image)
                    images_to_save.append(combined_array)

                    # 清理GPU内存
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()

            except FileNotFoundError:
                logger.warning(f"图片文件不存在: {image_path}")
                continue
            except Exception as e:
                logger.warning(f"处理图片 {image_path} 时出错: {e}")
                continue
            finally:
                vis_pbar.update(1)

        vis_pbar.close()
        
        # 分组拼接并保存
        if images_to_save:
            try:
                save_grouped_images(
                    images_to_save,
                    self.latest_dir,
                    self.running_steps_dir,
                    global_step
                )
                logger.info(f"可视化结果已保存，共处理 {len(images_to_save)} 张图片")

                # 输出图像尺寸信息用于调试
                if images_to_save:
                    sample_shape = images_to_save[0].shape
                    logger.debug(f"可视化图像尺寸: {sample_shape}")

            except Exception as e:
                logger.error(f"保存可视化结果时出错: {e}")
                # 尝试单独保存每张图片
                for i, img_array in enumerate(images_to_save):
                    try:
                        img_pil = Image.fromarray(img_array)
                        filename = f"visualization_single_{global_step:09d}_{i:03d}.png"
                        img_pil.save(os.path.join(self.latest_dir, filename))
                        logger.debug(f"单独保存图片: {filename}")
                    except Exception as single_save_e:
                        logger.error(f"单独保存图片 {i} 失败: {single_save_e}")
        else:
            logger.warning("没有成功处理的可视化图片")
    
    def prepare_visualization_samples(self) -> List[str]:
        """
        准备可视化样本图片路径列表
        
        Returns:
            排序后的图片路径列表（前max_samples个）
        """
        sample_dir = Path(self.sample_images_dir)
        if not sample_dir.exists():
            logger.error(f"可视化样本目录不存在: {sample_dir}")
            return []
        
        # 支持的图片格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        # 收集所有图片文件
        image_paths: List[Path] = []
        for ext in image_extensions:
            image_paths.extend(sample_dir.glob(f'*{ext}'))
            image_paths.extend(sample_dir.glob(f'*{ext.upper()}'))
        
        # 按文件名排序并转换为字符串
        image_paths_str = sorted([str(path) for path in image_paths])

        # 限制样本数量
        if len(image_paths_str) > self.max_samples:
            image_paths_str = image_paths_str[:self.max_samples]

        logger.info(f"准备了 {len(image_paths_str)} 个可视化样本")
        return image_paths_str
    
    def process_single_sample(
        self,
        image_path: str,
        model: torch.nn.Module
    ) -> Optional[Image.Image]:
        """
        处理单个样本的完整可视化流程
        
        Args:
            image_path: 图片文件路径
            model: CycleCenterNet模型
            
        Returns:
            组合可视化图片，如果处理失败则返回None
        """
        try:
            # 加载并预处理图像
            original_image, preprocessed_tensor = load_and_preprocess_image(
                image_path=image_path,
                target_size=self.target_size,
                mean=self.mean,
                std=self.std,
                to_rgb=self.to_rgb
            )
            
            # 模型推理
            input_tensor = preprocessed_tensor.unsqueeze(0).to(self.device, self.weight_dtype)

            # 确保模型在eval模式下进行推理，避免分布式通信问题
            model.eval()

            with torch.no_grad():
                model_outputs = model(input_tensor)

            # 添加详细的模型输出调试信息
            logger.debug(f"模型输出数量: {len(model_outputs)}")
            for i, output in enumerate(model_outputs):
                logger.debug(f"输出 {i}: shape={output.shape}, dtype={output.dtype}, "
                             f"min={output.min().item():.4f}, max={output.max().item():.4f}, "
                             f"mean={output.mean().item():.4f}")

            # 解析模型输出并提取预测结果
            predictions = self._extract_predictions_from_outputs(model_outputs, original_image.size)
            
            # 创建组合可视化
            combined_image = self.create_combined_visualization(original_image, predictions, model_outputs)
            
            return combined_image
            
        except Exception as e:
            logger.error(f"处理样本 {image_path} 时出错: {e}")
            return None
    
    def _extract_predictions_from_outputs(
        self,
        model_outputs: Tuple[torch.Tensor, ...],
        original_size: Tuple[int, int]
    ) -> Dict[str, Any]:
        """
        从模型输出中提取预测结果

        Args:
            model_outputs: 模型输出元组 (heatmap, offset, center2vertex, vertex2center)
            original_size: 原图尺寸 (width, height)

        Returns:
            预测结果字典，包含center_points和bboxes
        """
        predictions = {}
        center_points_rel = []  # 初始化变量

        if len(model_outputs) >= 1:
            # 第一个输出是中心点热图
            heatmap = model_outputs[0].squeeze(0).squeeze(0)  # 移除batch和channel维度

            # 从热图中提取中心点（相对坐标）
            center_points_rel = self._extract_center_points_from_heatmap(heatmap, original_size)

            # 转换为图像绝对坐标
            img_width, img_height = original_size
            center_points_abs = []
            for rel_x, rel_y in center_points_rel:
                abs_x = rel_x * img_width
                abs_y = rel_y * img_height
                center_points_abs.append((abs_x, abs_y))

            predictions['center_points'] = center_points_abs

            # 添加调试信息
            logger.debug(f"热图统计: min={heatmap.min().item():.4f}, max={heatmap.max().item():.4f}, "
                        f"mean={heatmap.mean().item():.4f}, 检测到{len(center_points_abs)}个中心点")

        # 生成边界框预测
        if len(model_outputs) >= 3 and len(center_points_rel) > 0:
            # 检查是否有offset输出
            offset = None
            if len(model_outputs) >= 2:
                offset = model_outputs[1].squeeze(0)  # 移除batch维度，保持(2, H, W)
                logger.debug(f"Offset统计: min={offset.min().item():.6f}, max={offset.max().item():.6f}, "
                            f"mean={offset.mean().item():.6f}, std={offset.std().item():.6f}")

            # 使用center2vertex预测生成边界框
            center2vertex = model_outputs[2].squeeze(0)  # 移除batch维度，保持(8, H, W)
            logger.debug(f"Center2Vertex统计: min={center2vertex.min().item():.6f}, max={center2vertex.max().item():.6f}, "
                         f"mean={center2vertex.mean().item():.6f}, std={center2vertex.std().item():.6f}")

            # 检查是否有vertex2center输出
            if len(model_outputs) >= 4:
                vertex2center = model_outputs[3].squeeze(0)  # 移除batch维度，保持(8, H, W)
                logger.debug(f"Vertex2Center统计: min={vertex2center.min().item():.6f}, max={vertex2center.max().item():.6f}, "
                             f"mean={vertex2center.mean().item():.6f}, std={vertex2center.std().item():.6f}")

            heatmap_shape = (heatmap.shape[0], heatmap.shape[1])  # (H, W)

            bboxes = self._generate_bboxes_from_center2vertex(
                center_points_rel, center2vertex, heatmap_shape, original_size, offset
            )
            predictions['bboxes'] = bboxes

            logger.debug(f"生成了{len(bboxes)}个边界框")
        else:
            predictions['bboxes'] = []
            if len(center_points_rel) > 0:
                logger.warning("模型输出不完整，无法生成边界框")

        return predictions
    
    def _extract_center_points_from_heatmap(
        self,
        heatmap: torch.Tensor,
        original_size: Tuple[int, int],
        threshold: float = 0.1,
        max_detections: int = 50,
        kernel_size: int = 3
    ) -> List[Tuple[float, float]]:
        """
        从热图中提取中心点（改进版本，添加峰值检测和NMS）

        Args:
            heatmap: 热图张量 (H, W)
            original_size: 原图尺寸 (width, height) - 用于后续扩展
            threshold: 检测阈值，降低到0.1
            max_detections: 最大检测数量
            kernel_size: 局部最大值检测的核大小

        Returns:
            中心点列表，坐标为相对坐标 [0, 1]
        """
        # 注意：original_size参数预留用于后续扩展，当前版本暂未使用
        _ = original_size  # 避免未使用参数警告

        # 确保heatmap在正确的设备上
        device = heatmap.device

        # 添加batch和channel维度用于max_pool2d
        heatmap_4d = heatmap.unsqueeze(0).unsqueeze(0)  # (1, 1, H, W)

        # 局部最大值检测（非极大值抑制）
        pad = (kernel_size - 1) // 2
        hmax = torch.nn.functional.max_pool2d(
            heatmap_4d, kernel_size, stride=1, padding=pad
        )

        # 保留局部最大值点
        keep = (hmax == heatmap_4d).float()
        heatmap_nms = heatmap_4d * keep

        # 移除添加的维度
        heatmap_nms = heatmap_nms.squeeze(0).squeeze(0)  # (H, W)

        # 应用阈值
        heatmap_nms = torch.where(heatmap_nms > threshold, heatmap_nms, torch.zeros_like(heatmap_nms))

        # 转换为numpy进行后续处理
        heatmap_np = heatmap_nms.detach().cpu().numpy()

        # 找到所有非零点
        y_coords, x_coords = np.where(heatmap_np > 0)

        if len(x_coords) == 0:
            return []

        # 获取对应的分数
        scores = heatmap_np[y_coords, x_coords]

        # 按分数排序，取前max_detections个
        if len(scores) > max_detections:
            top_indices = np.argsort(scores)[-max_detections:]
            x_coords = x_coords[top_indices]
            y_coords = y_coords[top_indices]

        # 转换为相对坐标
        h, w = heatmap_np.shape
        center_points = []

        for x, y in zip(x_coords, y_coords):
            # 转换为相对坐标
            rel_x = x / w
            rel_y = y / h
            center_points.append((rel_x, rel_y))

        return center_points
    
    def _generate_bboxes_from_centers(
        self,
        center_points: List[Tuple[float, float]],
        bbox_size: float = 0.03
    ) -> List[Tuple[float, float, float, float]]:
        """
        基于中心点生成边界框（简单演示用）

        Args:
            center_points: 中心点列表
            bbox_size: 边界框大小（相对尺寸），减小到0.03

        Returns:
            边界框列表，格式为 [x_min, y_min, x_max, y_max]
        """
        bboxes = []
        half_size = bbox_size / 2

        for x, y in center_points:
            x_min = max(0, x - half_size)
            y_min = max(0, y - half_size)
            x_max = min(1, x + half_size)
            y_max = min(1, y + half_size)

            bboxes.append((x_min, y_min, x_max, y_max))

        return bboxes

    def _generate_bboxes_with_offset(
        self,
        center_points: List[Tuple[float, float]],
        offset: torch.Tensor,
        heatmap_shape: Tuple[int, int],
        default_size: float = 0.03
    ) -> List[Tuple[float, float, float, float]]:
        """
        基于中心点和偏移信息生成边界框

        Args:
            center_points: 中心点列表（相对坐标）
            offset: 偏移张量 (2, H, W)
            heatmap_shape: 热图尺寸 (H, W)
            default_size: 默认边界框大小

        Returns:
            边界框列表，格式为 [x_min, y_min, x_max, y_max]
        """
        bboxes = []
        h, w = heatmap_shape

        for rel_x, rel_y in center_points:
            # 转换为热图坐标
            x_idx = int(rel_x * w)
            y_idx = int(rel_y * h)

            # 确保索引在有效范围内
            x_idx = max(0, min(w - 1, x_idx))
            y_idx = max(0, min(h - 1, y_idx))

            # 获取偏移值
            offset_x = offset[0, y_idx, x_idx].item()
            offset_y = offset[1, y_idx, x_idx].item()

            # 应用偏移（这里简化处理，实际可能需要更复杂的逻辑）
            corrected_x = rel_x + offset_x / w
            corrected_y = rel_y + offset_y / h

            # 生成边界框
            half_size = default_size / 2
            x_min = max(0, corrected_x - half_size)
            y_min = max(0, corrected_y - half_size)
            x_max = min(1, corrected_x + half_size)
            y_max = min(1, corrected_y + half_size)

            bboxes.append((x_min, y_min, x_max, y_max))

        return bboxes
    
    def create_combined_visualization(
        self,
        original_image: Image.Image,
        predictions: Dict[str, Any],
        model_outputs: Tuple[torch.Tensor, ...]
    ) -> Image.Image:
        """
        创建组合可视化图片（原图+预测 | 热图）
        
        Args:
            original_image: 原始图像
            predictions: 预测结果字典
            model_outputs: 模型输出
            
        Returns:
            组合可视化图片
        """
        # 在原图上绘制预测结果
        image_with_predictions = draw_predictions_on_image(
            image=original_image,
            predictions=predictions,
            transparency=self.transparency,
            bbox_color=tuple(self.bbox_color),
            keypoint_color=tuple(self.keypoint_color),
            center_color=tuple(self.center_color),
            line_thickness=self.line_thickness,
            point_radius=self.point_radius
        )
        
        # 创建热图可视化
        if len(model_outputs) >= 1:
            heatmap_tensor = model_outputs[0].squeeze(0).squeeze(0)  # 移除batch和channel维度
            heatmap_image = create_heatmap_visualization(
                heatmap_tensor=heatmap_tensor,
                colormap=self.colormap,
                normalize=self.normalize,
                threshold=self.threshold
            )
            
            # 调整热图尺寸匹配原图
            heatmap_image = heatmap_image.resize(original_image.size, Image.Resampling.LANCZOS)
        else:
            # 如果没有热图，创建空白图像
            heatmap_image = Image.new('RGB', original_image.size, (128, 128, 128))
        
        # 水平拼接图像
        combined_image = combine_images_horizontally(
            image_with_predictions,
            heatmap_image,
            resize_to_match=True
        )
        
        return combined_image


    def _generate_bboxes_from_center2vertex(
        self,
        center_points_rel: List[Tuple[float, float]],
        center2vertex: torch.Tensor,
        heatmap_shape: Tuple[int, int],
        original_size: Tuple[int, int],
        offset: Optional[torch.Tensor] = None
    ) -> List[Tuple[float, float, float, float]]:
        """
        从center2vertex预测生成精确的边界框

        Args:
            center_points_rel: 中心点列表（相对坐标 [0,1]）
            center2vertex: center2vertex预测张量 (8, H, W)
            heatmap_shape: 热图尺寸 (H, W)
            original_size: 原图尺寸 (width, height)
            offset: 可选的offset预测张量 (2, H, W)，用于修正中心点位置

        Returns:
            边界框列表，格式为 [x1, y1, x2, y2]（绝对坐标）
        """
        bboxes = []
        h, w = heatmap_shape
        img_width, img_height = original_size

        # 计算缩放比例
        scale_x = img_width / w
        scale_y = img_height / h

        for i, (rel_x, rel_y) in enumerate(center_points_rel):
            # 转换为特征图坐标
            feat_x = rel_x * w
            feat_y = rel_y * h
            feat_x_int = int(feat_x)
            feat_y_int = int(feat_y)

            # 确保坐标在有效范围内
            feat_x_int = max(0, min(w - 1, feat_x_int))
            feat_y_int = max(0, min(h - 1, feat_y_int))

            # 如果有offset预测，使用它来修正中心点位置
            if offset is not None:
                offset_x = offset[0, feat_y_int, feat_x_int].item()
                offset_y = offset[1, feat_y_int, feat_x_int].item()
                # 应用offset修正
                feat_x += offset_x
                feat_y += offset_y
                logger.debug(f"中心点 {i} offset修正: ({offset_x:.3f}, {offset_y:.3f})")
            else:
                logger.debug(f"中心点 {i} 无offset修正")

            # 从center2vertex预测中读取向量
            # center2vertex格式: [tl_x, tl_y, tr_x, tr_y, br_x, br_y, bl_x, bl_y]
            vectors = center2vertex[:, feat_y_int, feat_x_int].detach().cpu().numpy()

            # 添加详细调试信息
            logger.debug(f"中心点 {i}: rel=({rel_x:.3f}, {rel_y:.3f}), "
                        f"feat=({feat_x:.1f}, {feat_y:.1f}), "
                        f"feat_int=({feat_x_int}, {feat_y_int})")
            logger.debug(f"center2vertex向量: {vectors}")

            # 计算中心点在特征图上的精确坐标
            center_feat_x = feat_x
            center_feat_y = feat_y

            # 检查向量是否太小（接近零或数值过小）
            vector_magnitude = np.linalg.norm(vectors)
            if vector_magnitude < 1e-4:  # 如果向量幅度小于0.0001（降低阈值）
                logger.warning(f"中心点 {i} 的center2vertex向量太小 (magnitude={vector_magnitude:.6f})，使用默认边界框大小")
                # 使用更大的默认边界框大小（相对于特征图的固定比例）
                default_half_width = 20.0  # 特征图上的像素，增大到20
                default_half_height = 15.0  # 特征图上的像素，增大到15

                tl_x = center_feat_x - default_half_width
                tl_y = center_feat_y - default_half_height
                tr_x = center_feat_x + default_half_width
                tr_y = center_feat_y - default_half_height
                br_x = center_feat_x + default_half_width
                br_y = center_feat_y + default_half_height
                bl_x = center_feat_x - default_half_width
                bl_y = center_feat_y + default_half_height
            else:
                # 使用预测的center2vertex向量计算四个顶点
                logger.debug(f"中心点 {i} 使用预测向量 (magnitude={vector_magnitude:.6f})")
                # 计算四个顶点在特征图上的坐标
                tl_x = center_feat_x + vectors[0]  # top-left
                tl_y = center_feat_y + vectors[1]
                tr_x = center_feat_x + vectors[2]  # top-right
                tr_y = center_feat_y + vectors[3]
                br_x = center_feat_x + vectors[4]  # bottom-right
                br_y = center_feat_y + vectors[5]
                bl_x = center_feat_x + vectors[6]  # bottom-left
                bl_y = center_feat_y + vectors[7]

            logger.debug(f"顶点坐标(特征图): TL=({tl_x:.1f},{tl_y:.1f}), "
                         f"TR=({tr_x:.1f},{tr_y:.1f}), "
                         f"BR=({br_x:.1f},{br_y:.1f}), "
                         f"BL=({bl_x:.1f},{bl_y:.1f})")

            # 计算边界框（取四个顶点的外接矩形）
            min_x = min(tl_x, tr_x, br_x, bl_x)
            max_x = max(tl_x, tr_x, br_x, bl_x)
            min_y = min(tl_y, tr_y, br_y, bl_y)
            max_y = max(tl_y, tr_y, br_y, bl_y)

            # 转换为图像坐标
            x1 = max(0, min_x * scale_x)
            y1 = max(0, min_y * scale_y)
            x2 = min(img_width, max_x * scale_x)
            y2 = min(img_height, max_y * scale_y)

            logger.debug(f"边界框(图像坐标): ({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f})")

            bboxes.append((x1, y1, x2, y2))

        return bboxes
