# Time: 2025-07-13
# Author: <EMAIL>
# FileName: table_structure_visualizer_ms.py

"""
表格结构可视化器 (ModelScope 版本)

完全遵循 ModelScope 推理流程的可视化器，包括：
1. ModelScope 版本的预处理流程
2. 双通道热力图推理
3. bbox_decode 和 gbox_decode 解码算法
4. group_bbox_by_gbox 几何约束优化
5. 与现有可视化器相同的接口设计

参考文件:
- @modelscope/modelscope/pipelines/cv/table_recognition_pipeline.py
- @modelscope/modelscope/pipelines/cv/ocr_utils/table_process.py
"""

import os
import math
import torch
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

import cv2
from PIL import Image
from tqdm import tqdm

# 导入OmegaConf相关类型
from omegaconf import ListConfig
from modules.utils.log import LOGGER

from .image_utils import (
    draw_predictions_on_image,
    create_heatmap_visualization,
    combine_images_horizontally,
    save_grouped_images
)

logger = LOGGER


class TableStructureVisualizerMS:
    """
    表格结构可视化器 (ModelScope 版本)
    
    严格遵循 ModelScope 推理流程的可视化器，与现有可视化器保持相同的接口。
    """
    
    def __init__(
        self,
        config: Any,
        device: torch.device,
        weight_dtype: torch.dtype
    ):
        """
        初始化可视化器
        
        Args:
            config: 完整配置对象，包含visualization配置节
            device: 推理设备（GPU/CPU）
            weight_dtype: 模型权重数据类型
        """
        self.config = config
        self.device = device
        self.weight_dtype = weight_dtype
        
        # 可视化配置
        self.vis_config = getattr(config, 'visualization', None)
        if self.vis_config is None:
            raise ValueError("配置文件中缺少visualization配置节")
        
        self.enabled = getattr(self.vis_config, 'enabled', False)
        self.sample_images_dir = getattr(self.vis_config, 'sample_images_dir', 'assets/vis4tsr')
        self.max_samples = getattr(self.vis_config, 'max_samples', 10)
        self.frequency = getattr(self.vis_config, 'frequency', 1)

        # 处理output_dir配置：如果为null，则使用basic.output_dir/visualization_results
        vis_output_dir = getattr(self.vis_config, 'output_dir', None)
        if vis_output_dir is None:
            self.output_dir = os.path.join(config.basic.output_dir, 'visualization_results')
        else:
            self.output_dir = vis_output_dir

        # 可视化计数器
        self.visualization_counter = 0
        
        # 样式配置
        style_config = getattr(self.vis_config, 'style', {})
        self.bbox_color = getattr(style_config, 'bbox_color', [0, 255, 0])
        self.keypoint_color = getattr(style_config, 'keypoint_color', [255, 0, 0])
        self.center_color = getattr(style_config, 'center_color', [255, 0, 0])
        self.transparency = getattr(style_config, 'transparency', 0.8)
        self.line_thickness = getattr(style_config, 'line_thickness', 2)
        self.point_radius = getattr(style_config, 'point_radius', 4)
        
        # 热图配置
        heatmap_config = getattr(self.vis_config, 'heatmap', {})
        self.colormap = getattr(heatmap_config, 'colormap', 'jet')
        self.normalize = getattr(heatmap_config, 'normalize', True)
        self.threshold = getattr(heatmap_config, 'threshold', 0.1)

        # 修复 ListConfig 类型问题：将 OmegaConf 的 ListConfig 转换为普通 Python 列表
        display_channel = getattr(heatmap_config, 'display_channel', [0, 1])
        if isinstance(display_channel, ListConfig):
            self.display_channel = list(display_channel)  # 转换为普通列表
        else:
            self.display_channel = display_channel  # 支持单通道或双通道

        # ModelScope 推理参数 - 从配置文件读取
        ms_config = getattr(self.vis_config, 'modelscope', {})
        # 移除冗余的use_modelscope_pipeline参数，因为此类专门为ModelScope版本设计
        self.K = getattr(ms_config, 'bbox_k', 1000)  # bbox 检测数量
        self.MK = getattr(ms_config, 'gbox_k', 4000)  # gbox 检测数量
        self.confidence_threshold = getattr(ms_config, 'confidence_threshold', 0.3)
        self.nms_threshold = getattr(ms_config, 'nms_threshold', 0.3)
        
        # ModelScope 预处理参数
        self.inp_height, self.inp_width = 1024, 1024
        self.mean = np.array([0.408, 0.447, 0.470], dtype=np.float32).reshape(1, 1, 3)
        self.std = np.array([0.289, 0.274, 0.278], dtype=np.float32).reshape(1, 1, 3)
        
        logger.info(f"TableStructureVisualizerMS 初始化完成 (ModelScope 版本)")

    def visualize_validation_samples(
        self,
        model: torch.nn.Module,
        global_step: int,
        accelerator: Any
    ) -> None:
        """
        主要的可视化入口函数，与现有可视化器接口一致
        
        Args:
            model: 训练好的 CycleCenterNet 模型 (ModelScope 版本)
            global_step: 当前训练步数
            accelerator: accelerate 框架对象
        """
        if not self.enabled:
            logger.info("可视化功能未启用，跳过可视化")
            return

        # 检查可视化频率
        self.visualization_counter += 1
        if self.visualization_counter % self.frequency != 0:
            logger.info(f"跳过可视化（频率控制：{self.visualization_counter}/{self.frequency}）")
            return

        logger.info(f"开始生成步数 {global_step} 的可视化结果（ModelScope 版本）")
        
        # 准备可视化样本
        image_paths = self.prepare_visualization_samples()
        if not image_paths:
            logger.warning("未找到可视化样本图片")
            return

        # 设置模型为评估模式
        model.eval()
        
        # 处理样本并生成可视化
        visualization_results = []
        
        with torch.no_grad():
            for image_path in tqdm(image_paths, desc="生成可视化"):
                try:
                    result = self.process_single_sample(image_path, model)
                    if result is not None:
                        visualization_results.append({
                            'image': result,
                            'filename': Path(image_path).stem
                        })
                except Exception as e:
                    logger.error(f"处理样本 {image_path} 时出错: {e}")
                    continue
        
        # 保存可视化结果
        if visualization_results:
            self.save_grouped_images(visualization_results, global_step)
            logger.info(f"可视化完成，共生成 {len(visualization_results)} 张图片")
        else:
            logger.warning("未生成任何可视化结果")

    def prepare_visualization_samples(self) -> List[str]:
        """
        准备可视化样本图片路径列表
        
        Returns:
            图片路径列表
        """
        if not os.path.exists(self.sample_images_dir):
            logger.warning(f"可视化样本目录不存在: {self.sample_images_dir}")
            return []
        
        # 支持的图片格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        # 收集图片文件
        image_paths = []
        for file_path in Path(self.sample_images_dir).rglob('*'):
            if file_path.suffix.lower() in image_extensions:
                image_paths.append(str(file_path))
        
        # 排序并限制数量
        image_paths.sort()
        if len(image_paths) > self.max_samples:
            image_paths = image_paths[:self.max_samples]
        
        logger.info(f"找到 {len(image_paths)} 张可视化样本图片")
        return image_paths

    def process_single_sample(
        self,
        image_path: str,
        model: torch.nn.Module
    ) -> Optional[Image.Image]:
        """
        处理单个样本，执行完整的 ModelScope 推理流程
        
        Args:
            image_path: 图片路径
            model: CycleCenterNet 模型
            
        Returns:
            组合可视化图片
        """
        try:
            # 1. ModelScope 预处理
            original_image, processed_tensor, meta = self._modelscope_preprocess(image_path)
            
            # 2. 模型推理
            with torch.no_grad():
                model_outputs = model(processed_tensor)
            
            # 3. ModelScope 后处理
            predictions = self._modelscope_postprocess(model_outputs, meta)
            
            # 4. 创建可视化
            combined_image = self.create_combined_visualization(
                original_image, predictions, model_outputs
            )
            
            return combined_image
            
        except Exception as e:
            logger.error(f"处理样本 {image_path} 时出错: {e}")
            return None

    def _modelscope_preprocess(self, image_path: str) -> Tuple[Image.Image, torch.Tensor, Dict]:
        """
        ModelScope 版本的预处理流程
        严格遵循 @modelscope/modelscope/pipelines/cv/table_recognition_pipeline.py
        
        Args:
            image_path: 图片路径
            
        Returns:
            (原始图像, 处理后张量, 元数据)
        """
        # 加载图像
        original_image = Image.open(image_path).convert('RGB')
        img = np.array(original_image)[:, :, ::-1]  # RGB -> BGR
        
        height, width = img.shape[0:2]
        
        # 计算仿射变换参数
        c = np.array([width / 2., height / 2.], dtype=np.float32)
        s = max(height, width) * 1.0
        
        # 获取仿射变换矩阵
        trans_input = self._get_affine_transform(c, s, 0, [self.inp_width, self.inp_height])
        
        # 应用仿射变换
        resized_image = cv2.resize(img, (width, height))
        inp_image = cv2.warpAffine(
            resized_image,
            trans_input, (self.inp_width, self.inp_height),
            flags=cv2.INTER_LINEAR)
        
        # 归一化
        inp_image = ((inp_image / 255. - self.mean) / self.std).astype(np.float32)
        
        # 转换为张量
        images = inp_image.transpose(2, 0, 1).reshape(1, 3, self.inp_height, self.inp_width)
        images = torch.from_numpy(images).to(self.device)
        
        # 元数据
        meta = {
            'c': c,
            's': s,
            'input_height': self.inp_height,
            'input_width': self.inp_width,
            'out_height': self.inp_height // 4,
            'out_width': self.inp_width // 4,
            'original_size': (width, height)
        }
        
        return original_image, images, meta

    def _get_affine_transform(self, center, scale, rot, output_size, shift=np.array([0, 0], dtype=np.float32), inv=0):
        """
        获取仿射变换矩阵
        严格遵循 ModelScope 原始实现
        """
        if not isinstance(scale, np.ndarray) and not isinstance(scale, list):
            scale = np.array([scale, scale], dtype=np.float32)

        scale_tmp = scale
        src_w = scale_tmp[0]
        dst_w = output_size[0]
        dst_h = output_size[1]

        rot_rad = np.pi * rot / 180
        src_dir = self._get_dir([0, src_w * -0.5], rot_rad)
        dst_dir = np.array([0, dst_w * -0.5], np.float32)

        src = np.zeros((3, 2), dtype=np.float32)
        dst = np.zeros((3, 2), dtype=np.float32)
        src[0, :] = center + scale_tmp * shift
        src[1, :] = center + src_dir + scale_tmp * shift
        dst[0, :] = [dst_w * 0.5, dst_h * 0.5]
        dst[1, :] = np.array([dst_w * 0.5, dst_h * 0.5], np.float32) + dst_dir

        src[2:, :] = self._get_3rd_point(src[0, :], src[1, :])
        dst[2:, :] = self._get_3rd_point(dst[0, :], dst[1, :])

        if inv:
            trans = cv2.getAffineTransform(np.float32(dst), np.float32(src))
        else:
            trans = cv2.getAffineTransform(np.float32(src), np.float32(dst))

        return trans

    def _get_dir(self, src_point, rot_rad):
        """获取方向向量"""
        sn, cs = np.sin(rot_rad), np.cos(rot_rad)
        src_result = [0, 0]
        src_result[0] = src_point[0] * cs - src_point[1] * sn
        src_result[1] = src_point[0] * sn + src_point[1] * cs
        return src_result

    def _get_3rd_point(self, a, b):
        """获取第三个点"""
        direct = a - b
        return b + np.array([-direct[1], direct[0]], dtype=np.float32)

    def _modelscope_postprocess(self, model_outputs: List[Dict], meta: Dict) -> Dict[str, Any]:
        """
        ModelScope 版本的后处理流程
        严格遵循 @modelscope/modelscope/pipelines/cv/table_recognition_pipeline.py

        Args:
            model_outputs: 模型输出列表
            meta: 预处理元数据

        Returns:
            预测结果字典
        """
        output = model_outputs[0]  # 取第一个输出

        # 获取模型输出
        hm = output['hm']    # [1, 2, 256, 256]
        v2c = output['v2c']  # [1, 8, 256, 256]
        c2v = output['c2v']  # [1, 8, 256, 256]
        reg = output['reg']  # [1, 2, 256, 256]

        # 解码边界框（使用第1通道）
        logger.debug(f"热图形状: {hm.shape}, 第1通道最大值: {hm[:, 0:1, :, :].max():.4f}")
        bbox, _ = self._bbox_decode(hm[:, 0:1, :, :], c2v, reg=reg, K=self.K)

        # 解码顶点（使用第2通道）
        logger.debug(f"第2通道最大值: {hm[:, 1:2, :, :].max():.4f}")
        gbox, _ = self._gbox_decode(hm[:, 1:2, :, :], v2c, reg=reg, K=self.MK)

        # 转换为numpy
        bbox = bbox.detach().cpu().numpy()
        gbox = gbox.detach().cpu().numpy()

        logger.debug(f"解码后bbox形状: {bbox.shape}, gbox形状: {gbox.shape}")
        if len(bbox) > 0:
            logger.debug(f"bbox最高置信度: {bbox[0][:, 8].max():.4f}")
        if len(gbox) > 0:
            logger.debug(f"gbox最高置信度: {gbox[0][:, 10].max():.4f}")

        # NMS - 使用配置的阈值
        bbox = self._nms_bbox(bbox, self.nms_threshold)

        # 坐标变换
        bbox = self._bbox_post_process(
            bbox.copy(), [meta['c']], [meta['s']],
            meta['out_height'], meta['out_width']
        )
        gbox = self._gbox_post_process(
            gbox.copy(), [meta['c']], [meta['s']],
            meta['out_height'], meta['out_width']
        )

        # 几何约束优化
        bbox = self._group_bbox_by_gbox(bbox[0], gbox[0])

        # 过滤低置信度结果 - 使用配置的阈值
        final_boxes = []
        logger.debug(f"原始bbox数量: {len(bbox)}, 置信度阈值: {self.confidence_threshold}")

        for i, box in enumerate(bbox):
            confidence = box[8] if len(box) > 8 else 0.0
            logger.debug(f"Box {i}: confidence={confidence:.4f}")
            if confidence > self.confidence_threshold:  # 使用配置的置信度阈值
                final_boxes.append(box[0:8])  # 只保留坐标

        print()
        logger.info(f"过滤后的检测框数量: {len(final_boxes)}/{len(bbox)}")

        # 如果没有检测到任何框，降低阈值重试
        if len(final_boxes) == 0 and len(bbox) > 0:
            # 找到最高置信度
            max_confidence = max(box[8] if len(box) > 8 else 0.0 for box in bbox)
            logger.warning(f"未检测到置信度>{self.confidence_threshold}的框，最高置信度={max_confidence:.4f}")

            # 使用更低的阈值，或者最高置信度的一半
            fallback_threshold = min(0.1, max_confidence * 0.5)
            logger.warning(f"尝试降低阈值到{fallback_threshold:.4f}")

            for box in bbox:
                confidence = box[8] if len(box) > 8 else 0.0
                if confidence > fallback_threshold:  # 降低阈值
                    final_boxes.append(box[0:8])
            logger.info(f"降低阈值后的检测框数量: {len(final_boxes)}")

            # 如果还是没有，至少取前几个最高置信度的
            if len(final_boxes) == 0 and len(bbox) > 0:
                logger.warning("仍然没有检测框，取前3个最高置信度的框进行可视化")
                # 按置信度排序
                sorted_boxes = sorted(bbox, key=lambda x: x[8] if len(x) > 8 else 0.0, reverse=True)
                for box in sorted_boxes[:3]:  # 取前3个
                    final_boxes.append(box[0:8])
                logger.info(f"强制取前3个框后的数量: {len(final_boxes)}")

        # 准备多边形坐标
        polygons = np.array(final_boxes) if final_boxes else np.array([])

        # 从多边形中提取边界框和中心点
        bboxes = []
        center_points = []

        if len(polygons) > 0:
            for poly in polygons:
                # 多边形格式: [x1, y1, x2, y2, x3, y3, x4, y4]
                # 计算边界框 (min_x, min_y, max_x, max_y)
                x_coords = poly[0::2]  # 所有x坐标
                y_coords = poly[1::2]  # 所有y坐标

                x_min, x_max = np.min(x_coords), np.max(x_coords)
                y_min, y_max = np.min(y_coords), np.max(y_coords)

                bboxes.append([x_min, y_min, x_max, y_max])

                # 计算中心点
                center_x = np.mean(x_coords)
                center_y = np.mean(y_coords)
                center_points.append([center_x, center_y])

        return {
            'polygons': polygons,
            'bboxes': np.array(bboxes),
            'center_points': np.array(center_points),
            'raw_bbox': bbox,
            'raw_gbox': gbox[0] if len(gbox) > 0 else np.array([])
        }

    def _topk(self, scores, K=40):
        """
        Top-K 选择
        严格遵循 ModelScope 原始实现
        """
        batch, cat, height, width = scores.size()

        topk_scores, topk_inds = torch.topk(scores.view(batch, cat, -1), K)

        topk_inds = topk_inds % (height * width)
        topk_ys = (topk_inds / width).int().float()
        topk_xs = (topk_inds % width).int().float()

        topk_score, topk_ind = torch.topk(topk_scores.view(batch, -1), K)
        topk_clses = (topk_ind / K).int()
        topk_inds = self._gather_feat(topk_inds.view(batch, -1, 1),
                                     topk_ind).view(batch, K)
        topk_ys = self._gather_feat(topk_ys.view(batch, -1, 1), topk_ind).view(batch, K)
        topk_xs = self._gather_feat(topk_xs.view(batch, -1, 1), topk_ind).view(batch, K)

        return topk_score, topk_inds, topk_clses, topk_ys, topk_xs

    def _gather_feat(self, feat, ind, mask=None):
        """特征收集"""
        dim = feat.size(2)
        ind = ind.unsqueeze(2).expand(ind.size(0), ind.size(1), dim)
        feat = feat.gather(1, ind)
        if mask is not None:
            mask = mask.unsqueeze(2).expand_as(feat)
            feat = feat[mask]
            feat = feat.view(-1, dim)
        return feat

    def _tranpose_and_gather_feat(self, feat, ind):
        """转置并收集特征"""
        feat = feat.permute(0, 2, 3, 1).contiguous()
        feat = feat.view(feat.size(0), -1, feat.size(3))
        feat = self._gather_feat(feat, ind)
        return feat

    def _nms(self, heat, kernel=3):
        """非极大值抑制"""
        import torch.nn.functional as F
        pad = (kernel - 1) // 2
        hmax = F.max_pool2d(heat, (kernel, kernel), stride=1, padding=pad)
        keep = (hmax == heat).float()
        return heat * keep, keep

    def _bbox_decode(self, heat, wh, reg=None, K=100):
        """
        边界框解码
        严格遵循 ModelScope 原始实现
        """
        batch, cat, height, width = heat.size()

        heat, keep = self._nms(heat)

        scores, inds, clses, ys, xs = self._topk(heat, K=K)
        if reg is not None:
            reg = self._tranpose_and_gather_feat(reg, inds)
            reg = reg.view(batch, K, 2)
            xs = xs.view(batch, K, 1) + reg[:, :, 0:1]
            ys = ys.view(batch, K, 1) + reg[:, :, 1:2]
        else:
            xs = xs.view(batch, K, 1) + 0.5
            ys = ys.view(batch, K, 1) + 0.5
        wh = self._tranpose_and_gather_feat(wh, inds)
        wh = wh.view(batch, K, 8)
        clses = clses.view(batch, K, 1).float()
        scores = scores.view(batch, K, 1)

        bboxes = torch.cat(
            [
                xs - wh[..., 0:1],
                ys - wh[..., 1:2],
                xs - wh[..., 2:3],
                ys - wh[..., 3:4],
                xs - wh[..., 4:5],
                ys - wh[..., 5:6],
                xs - wh[..., 6:7],
                ys - wh[..., 7:8],
            ],
            dim=2,
        )
        detections = torch.cat([bboxes, scores, clses], dim=2)

        return detections, inds

    def _gbox_decode(self, mk, st_reg, reg=None, K=400):
        """
        顶点解码
        严格遵循 ModelScope 原始实现
        """
        batch, cat, height, width = mk.size()
        mk, keep = self._nms(mk)
        scores, inds, clses, ys, xs = self._topk(mk, K=K)
        if reg is not None:
            reg = self._tranpose_and_gather_feat(reg, inds)
            reg = reg.view(batch, K, 2)
            xs = xs.view(batch, K, 1) + reg[:, :, 0:1]
            ys = ys.view(batch, K, 1) + reg[:, :, 1:2]
        else:
            xs = xs.view(batch, K, 1) + 0.5
            ys = ys.view(batch, K, 1) + 0.5
        scores = scores.view(batch, K, 1)
        clses = clses.view(batch, K, 1).float()
        st_Reg = self._tranpose_and_gather_feat(st_reg, inds)
        bboxes = torch.cat(
            [
                xs - st_Reg[..., 0:1],
                ys - st_Reg[..., 1:2],
                xs - st_Reg[..., 2:3],
                ys - st_Reg[..., 3:4],
                xs - st_Reg[..., 4:5],
                ys - st_Reg[..., 5:6],
                xs - st_Reg[..., 6:7],
                ys - st_Reg[..., 7:8],
            ],
            dim=2,
        )
        return torch.cat([xs, ys, bboxes, scores, clses], dim=2), keep

    def _transform_preds(self, coords, center, scale, output_size, rot=0):
        """坐标变换"""
        target_coords = np.zeros(coords.shape)
        trans = self._get_affine_transform(center, scale, rot, output_size, inv=1)
        for p in range(coords.shape[0]):
            target_coords[p, 0:2] = self._affine_transform(coords[p, 0:2], trans)
        return target_coords

    def _affine_transform(self, pt, t):
        """仿射变换"""
        new_pt = np.array([pt[0], pt[1], 1.0], dtype=np.float32).T
        new_pt = np.dot(t, new_pt)
        return new_pt[:2]

    def _bbox_post_process(self, bbox, c, s, h, w):
        """
        边界框后处理
        严格遵循 ModelScope 原始实现
        """
        for i in range(bbox.shape[0]):
            bbox[i, :, 0:2] = self._transform_preds(bbox[i, :, 0:2], c[i], s[i], (w, h))
            bbox[i, :, 2:4] = self._transform_preds(bbox[i, :, 2:4], c[i], s[i], (w, h))
            bbox[i, :, 4:6] = self._transform_preds(bbox[i, :, 4:6], c[i], s[i], (w, h))
            bbox[i, :, 6:8] = self._transform_preds(bbox[i, :, 6:8], c[i], s[i], (w, h))
        return bbox

    def _gbox_post_process(self, gbox, c, s, h, w):
        """
        顶点后处理
        严格遵循 ModelScope 原始实现
        """
        for i in range(gbox.shape[0]):
            gbox[i, :, 0:2] = self._transform_preds(gbox[i, :, 0:2], c[i], s[i], (w, h))
            gbox[i, :, 2:4] = self._transform_preds(gbox[i, :, 2:4], c[i], s[i], (w, h))
            gbox[i, :, 4:6] = self._transform_preds(gbox[i, :, 4:6], c[i], s[i], (w, h))
            gbox[i, :, 6:8] = self._transform_preds(gbox[i, :, 6:8], c[i], s[i], (w, h))
            gbox[i, :, 8:10] = self._transform_preds(gbox[i, :, 8:10], c[i], s[i], (w, h))
        return gbox

    def _nms_bbox(self, dets, thresh):
        """
        边界框非极大值抑制
        严格遵循 ModelScope 原始实现
        """
        if len(dets) < 2:
            return dets
        index_keep = []
        keep = []
        for i in range(len(dets)):
            box = dets[i]
            if box[8] < thresh:
                break
            max_score_index = -1
            ctx = (dets[i][0] + dets[i][2] + dets[i][4] + dets[i][6]) / 4
            cty = (dets[i][1] + dets[i][3] + dets[i][5] + dets[i][7]) / 4
            for j in range(len(dets)):
                if i == j or dets[j][8] < thresh:
                    break
                x1, y1 = dets[j][0], dets[j][1]
                x2, y2 = dets[j][2], dets[j][3]
                x3, y3 = dets[j][4], dets[j][5]
                x4, y4 = dets[j][6], dets[j][7]
                a = (x2 - x1) * (cty - y1) - (y2 - y1) * (ctx - x1)
                b = (x3 - x2) * (cty - y2) - (y3 - y2) * (ctx - x2)
                c = (x4 - x3) * (cty - y3) - (y4 - y3) * (ctx - x3)
                d = (x1 - x4) * (cty - y4) - (y1 - y4) * (ctx - x4)
                if (a > 0 and b > 0 and c > 0 and d > 0) or (a < 0 and b < 0
                                                             and c < 0 and d < 0):
                    if dets[i][8] > dets[j][8] and max_score_index < 0:
                        max_score_index = i
                    elif dets[i][8] < dets[j][8]:
                        max_score_index = -2
                        break
            if max_score_index > -1:
                index_keep.append(max_score_index)
            elif max_score_index == -1:
                index_keep.append(i)
        for i in range(0, len(index_keep)):
            keep.append(dets[index_keep[i]])
        return np.array(keep)

    def _group_bbox_by_gbox(self, bboxes, gboxes, score_thred=None, v2c_dist_thred=2, c2v_dist_thred=0.5):
        """
        几何约束优化
        严格遵循 ModelScope 原始实现
        """
        import copy

        # 使用配置的置信度阈值，如果未提供则使用默认值
        if score_thred is None:
            score_thred = self.confidence_threshold

        # 调试信息
        logger.debug(f"_group_bbox_by_gbox: bboxes数量={len(bboxes)}, gboxes数量={len(gboxes)}")
        logger.debug(f"使用置信度阈值: {score_thred}")

        # 在训练早期阶段，可能需要降低阈值以便看到一些结果
        if len(bboxes) > 0 and bboxes[0][8] < score_thred:
            temp_thred = max(0.1, bboxes[0][8] * 0.9)  # 使用最高置信度的90%作为临时阈值
            logger.warning(f"所有bbox置信度低于阈值{score_thred}，临时降低到{temp_thred:.4f}")
            score_thred = temp_thred

        def point_in_box(box, point):
            x1, y1, x2, y2 = box[0], box[1], box[2], box[3]
            x3, y3, x4, y4 = box[4], box[5], box[6], box[7]
            ctx, cty = point[0], point[1]
            a = (x2 - x1) * (cty - y1) - (y2 - y1) * (ctx - x1)
            b = (x3 - x2) * (cty - y2) - (y3 - y2) * (ctx - x2)
            c = (x4 - x3) * (cty - y3) - (y4 - y3) * (ctx - x3)
            d = (x1 - x4) * (cty - y4) - (y1 - y4) * (ctx - x4)
            if (a > 0 and b > 0 and c > 0 and d > 0) or (a < 0 and b < 0 and c < 0
                                                         and d < 0):
                return True
            else:
                return False

        def get_distance(pt1, pt2):
            return math.sqrt((pt1[0] - pt2[0]) * (pt1[0] - pt2[0])
                             + (pt1[1] - pt2[1]) * (pt1[1] - pt2[1]))

        dets = copy.deepcopy(bboxes)
        sign = np.zeros((len(dets), 4))

        for idx, gbox in enumerate(gboxes):  # vertex x,y, gbox, score
            if gbox[10] < score_thred:
                break
            vertex = [gbox[0], gbox[1]]
            for i in range(0, 4):
                center = [gbox[2 * i + 2], gbox[2 * i + 3]]
                if get_distance(vertex, center) < v2c_dist_thred:
                    continue
                for k, bbox in enumerate(dets):
                    if bbox[8] < score_thred:
                        break
                    if sum(sign[k]) == 4:
                        continue
                    w = (abs(bbox[6] - bbox[0]) + abs(bbox[4] - bbox[2])) / 2
                    h = (abs(bbox[3] - bbox[1]) + abs(bbox[5] - bbox[7])) / 2
                    m = max(w, h)
                    if point_in_box(bbox, center):
                        min_dist, min_id = 1e4, -1
                        for j in range(0, 4):
                            dist = get_distance(vertex,
                                                [bbox[2 * j], bbox[2 * j + 1]])
                            if dist < min_dist:
                                min_dist = dist
                                min_id = j
                        if (min_id > -1 and min_dist < c2v_dist_thred * m
                                and sign[k][min_id] == 0):
                            bboxes[k][2 * min_id] = vertex[0]
                            bboxes[k][2 * min_id + 1] = vertex[1]
                            sign[k][min_id] = 1
        return bboxes

    def create_combined_visualization(
        self,
        original_image: Image.Image,
        predictions: Dict[str, Any],
        model_outputs: List[Dict]
    ) -> Image.Image:
        """
        创建组合可视化图片，支持双通道热力图显示

        Args:
            original_image: 原始图像
            predictions: 预测结果字典
            model_outputs: 模型输出

        Returns:
            组合可视化图片
        """
        # 在原图上绘制预测结果
        image_with_predictions = draw_predictions_on_image(
            image=original_image,
            predictions=predictions,
            transparency=self.transparency,
            bbox_color=tuple(self.bbox_color),
            keypoint_color=tuple(self.keypoint_color),
            center_color=tuple(self.center_color),
            line_thickness=self.line_thickness,
            point_radius=self.point_radius
        )

        # 创建热图可视化
        heatmap_images = []
        if len(model_outputs) >= 1 and 'hm' in model_outputs[0]:
            hm_tensor = model_outputs[0]['hm'].squeeze(0)  # [C, H, W]

            # 解析 display_channel 配置
            if isinstance(self.display_channel, (list, ListConfig)):
                # 显示多个通道（并排显示）
                channels_to_show = self.display_channel
                # 确保 channels_to_show 是普通的 Python 列表
                if isinstance(channels_to_show, ListConfig):
                    channels_to_show = list(channels_to_show)
            else:
                # 显示单个通道
                channels_to_show = [self.display_channel]

            # 生成指定通道的热图
            for channel_idx in channels_to_show:
                # 确保 channel_idx 是整数
                if isinstance(channel_idx, ListConfig):
                    channel_idx = int(channel_idx)

                if channel_idx < hm_tensor.shape[0]:
                    heatmap_image = create_heatmap_visualization(
                        heatmap_tensor=hm_tensor[channel_idx],
                        colormap=self.colormap,
                        normalize=self.normalize,
                        threshold=self.threshold
                    )
                    # 调整热图尺寸匹配原图
                    heatmap_image = heatmap_image.resize(original_image.size, Image.Resampling.LANCZOS)
                    heatmap_images.append(heatmap_image)
        else:
            # 如果没有热图，创建空白图像
            heatmap_images.append(Image.new('RGB', original_image.size, (128, 128, 128)))

        # 水平拼接图像：原图+预测 + 热图(s)
        images_to_combine = [image_with_predictions] + heatmap_images

        if len(images_to_combine) == 2:
            # 两张图片，使用现有函数
            combined_image = combine_images_horizontally(
                images_to_combine[0],
                images_to_combine[1],
                resize_to_match=True
            )
        else:
            # 多张图片，逐步拼接
            combined_image = images_to_combine[0]
            for img in images_to_combine[1:]:
                combined_image = combine_images_horizontally(
                    combined_image,
                    img,
                    resize_to_match=True
                )

        return combined_image

    def save_grouped_images(self, visualization_results: List[Dict], global_step: int):
        """
        保存分组的可视化图片，同时保存到步数目录和latest目录

        Args:
            visualization_results: 可视化结果列表
            global_step: 当前训练步数
        """
        # 复用现有的保存逻辑
        images = [result['image'] for result in visualization_results]
        filenames = [result['filename'] for result in visualization_results]

        # 保存到步数目录
        step_output_dir = Path(self.output_dir) / f"step_{global_step}"
        step_output_dir.mkdir(parents=True, exist_ok=True)

        # 保存到latest目录（覆盖保存最新步数的可视化效果）
        latest_output_dir = Path(self.output_dir) / "latest"
        latest_output_dir.mkdir(parents=True, exist_ok=True)

        for i, result in enumerate(visualization_results):
            filename = f"ms_{result['filename']}.png"

            # 保存到步数目录
            step_filepath = step_output_dir / filename
            result['image'].save(step_filepath)

            # 保存到latest目录（覆盖之前的文件）
            latest_filepath = latest_output_dir / filename
            result['image'].save(latest_filepath)

        logger.info(f"可视化图片已保存到: {step_output_dir}")
        logger.info(f"最新可视化图片已保存到: {latest_output_dir}")
