# Cycle-CenterNet ModelScope 版本

## 概述

这是 Cycle-CenterNet 的 ModelScope 版本实现，严格遵循 ModelScope 原始代码的架构和推理流程。与原有的 `cycle_centernet` 模块相比，这个版本支持双通道热力图、完整的几何约束优化和 ModelScope 权重兼容性。

## 主要特性

### 🔥 核心特性
- **双通道热力图**: 第1通道检测边界框中心点，第2通道检测顶点
- **完整推理流程**: bbox_decode、gbox_decode、group_bbox_by_gbox
- **权重兼容**: 支持加载 ModelScope 预训练权重
- **几何约束优化**: 基于顶点-中心点配对的几何约束

### 🏗️ 架构组件
- **DLA-34 骨干网络**: 严格按照 ModelScope 实现
- **双通道检测头**: 支持 hm(2), reg(2), c2v(8), v2c(8) 输出
- **损失函数**: 适配双通道的 GaussianFocalLoss + L1Loss
- **可视化器**: 完整的 ModelScope 推理流程可视化

## 模块结构

```
cycle_centernet_ms/
├── __init__.py                    # 模块入口和工厂函数
├── dla_backbone_ms.py             # DLA-34 骨干网络 (ModelScope 版本)
├── cycle_centernet_head_ms.py     # 双通道检测头
├── cycle_centernet_model_ms.py    # 完整模型组装
├── cycle_centernet_loss_ms.py     # 双通道损失函数
└── README.md                      # 本文档
```

## 快速开始

### 1. 创建模型

```python
from networks.cycle_centernet_ms import create_cycle_centernet_ms_model

# 基础模型
model = create_cycle_centernet_ms_model()

# 自定义配置
model = create_cycle_centernet_ms_model({
    'base_name': 'dla34',
    'pretrained': False,
    'down_ratio': 4,
    'head_conv': 256
})

# 加载 ModelScope 权重
model = create_cycle_centernet_ms_model({
    'checkpoint_path': '/path/to/modelscope/weights.pth'
})
```

### 2. 创建损失函数

```python
from networks.cycle_centernet_ms import create_cycle_centernet_ms_loss

# 默认权重
loss_fn = create_cycle_centernet_ms_loss()

# 自定义权重
loss_fn = create_cycle_centernet_ms_loss(
    heatmap_loss_weight=1.0,
    reg_loss_weight=1.0,
    c2v_loss_weight=1.0,
    v2c_loss_weight=0.5
)
```

### 3. 前向传播

```python
import torch

# 输入图像
x = torch.randn(1, 3, 1024, 1024)

# 模型推理
outputs = model(x)
print(f"输出格式: {type(outputs)}")  # List[Dict]
print(f"输出键: {list(outputs[0].keys())}")  # ['hm', 'reg', 'c2v', 'v2c']

# 检查输出形状
for key, value in outputs[0].items():
    print(f"{key}: {value.shape}")
# hm: torch.Size([1, 2, 256, 256])   # 双通道热力图
# reg: torch.Size([1, 2, 256, 256])  # 亚像素偏移
# c2v: torch.Size([1, 8, 256, 256])  # 中心到顶点
# v2c: torch.Size([1, 8, 256, 256])  # 顶点到中心
```

### 4. 损失计算

```python
# 准备目标
targets = {
    'heatmap_target': torch.zeros(1, 2, 256, 256),  # 双通道热力图目标
    'reg_target': torch.zeros(1, 2, 256, 256),      # 回归目标
    'c2v_target': torch.zeros(1, 8, 256, 256),      # 中心到顶点目标
    'v2c_target': torch.zeros(1, 8, 256, 256)       # 顶点到中心目标
}

# 计算损失
losses = loss_fn(outputs, targets, avg_factor=100)
print(f"损失项: {list(losses.keys())}")
# ['loss_heatmap', 'loss_reg', 'loss_c2v', 'loss_v2c']
```

## 与原版本对比

| 特性 | 原版本 (cycle_centernet) | ModelScope 版本 (cycle_centernet_ms) |
|------|-------------------------|-------------------------------------|
| 热力图通道 | 1 通道 | 2 通道 (bbox + vertex) |
| 检测头输出 | hm, reg, offset, wh | hm, reg, c2v, v2c |
| 损失函数 | GaussianFocalLoss + L1Loss | GaussianFocalLossMS + L1Loss |
| 推理流程 | 简化版本 | 完整 ModelScope 流程 |
| 几何约束 | 基础约束 | group_bbox_by_gbox |
| 权重兼容 | train-anything 权重 | ModelScope 权重 |

## 技术细节

### 双通道热力图
- **第1通道**: 边界框中心点检测，用于 bbox_decode
- **第2通道**: 顶点检测，用于 gbox_decode
- **损失计算**: 分别计算两个通道的 GaussianFocalLoss 并求和

### 回归头设计
- **reg**: 亚像素偏移 [B, 2, H, W]，用于精确定位
- **c2v**: 中心到顶点 [B, 8, H, W]，表示4个顶点相对中心的偏移
- **v2c**: 顶点到中心 [B, 8, H, W]，表示中心相对4个顶点的偏移

### 几何约束优化
ModelScope 版本实现了完整的几何约束优化算法：
1. **顶点-中心配对**: 基于距离阈值进行配对
2. **点在框内判断**: 使用叉积判断点是否在四边形内
3. **动态权重**: 基于预测误差计算配对权重

## 性能特点

### 模型规模
- **参数量**: ~18.6M (与原版本相同)
- **计算量**: 略高于原版本 (双通道热力图)
- **内存占用**: 相比原版本增加约 25%

### 训练特点
- **收敛速度**: 与原版本相当
- **精度提升**: 双通道设计提供更精确的检测
- **稳定性**: 几何约束提高训练稳定性

## 使用建议

### 1. 训练配置
```yaml
model:
  modelscope:
    base_name: "dla34"
    head_conv: 256
    down_ratio: 4

loss:
  weights:
    heatmap: 1.0
    reg: 1.0
    c2v: 1.0
    v2c: 0.5  # 通常设置较小的权重
```

### 2. 性能优化
- 使用混合精度训练减少内存占用
- 批次大小建议 8-16 (根据GPU内存调整)
- 学习率建议 0.00125 (Adam 优化器)

### 3. 调试技巧
- 使用可视化器检查双通道热力图
- 监控各损失项的变化趋势
- 检查几何约束的有效性

## 兼容性说明

### ModelScope 权重加载
```python
# 自动处理权重键名映射
model.load_modelscope_weights('/path/to/modelscope/weights.pth')
```

### train-anything 框架集成
- 完全兼容现有的训练循环
- 支持 accelerate 分布式训练
- 集成现有的数据加载和预处理

## 开发者信息

- **开发时间**: 2025-07-13
- **开发者**: <EMAIL>
- **版本**: ModelScope 兼容版本
- **依赖**: PyTorch, train-anything 框架

## 相关文档

- [使用指南](../../docs/ModelScope_CycleCenterNet_Usage.md)
- [训练脚本](../../training_loops/table_structure_recognition/train_cycle_centernet_ms.py)
- [配置文件](../../configs/table_structure_recognition/cycle_centernet/cycle_centernet_ms_config.yaml)
- [可视化器](../../modules/visualization/table_structure_visualizer_ms.py)
