# Time: 2025-07-13
# Author: <EMAIL>
# FileName: cycle_centernet_head_ms.py

"""
Cycle-CenterNet 检测头 (ModelScope 版本)

严格按照 ModelScope 原始实现的检测头，支持双通道热力图输出。
参考文件: @modelscope/modelscope/pipelines/cv/ocr_utils/model_dla34.py

关键特性:
1. 双通道热力图输出 [B, 2, H, W]
   - 第1通道: 边界框中心点检测
   - 第2通道: 顶点检测
2. 回归头输出:
   - reg: 亚像素偏移 [B, 2, H, W]
   - c2v: 中心到顶点 [B, 8, H, W]
   - v2c: 顶点到中心 [B, 8, H, W]
3. 与 ModelScope 完全一致的初始化策略
"""

import torch
import torch.nn as nn


def fill_fc_weights(layers):
    """
    初始化全连接层权重
    与 ModelScope 原始实现完全一致
    """
    for m in layers.modules():
        if isinstance(m, nn.Conv2d):
            nn.init.normal_(m.weight, std=0.001)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)


class CycleCenterNetHeadMS(nn.Module):
    """
    Cycle-CenterNet 检测头 (ModelScope 版本)
    
    实现双通道热力图和多个回归头的输出，
    严格遵循 ModelScope 原始实现。
    """

    def __init__(self, in_channels=64, head_conv=256):
        """
        初始化检测头
        
        Args:
            in_channels: 输入特征通道数
            head_conv: 中间层通道数
        """
        super(CycleCenterNetHeadMS, self).__init__()
        
        # 检测头配置，与 ModelScope 完全一致
        self.heads = {'hm': 2, 'v2c': 8, 'c2v': 8, 'reg': 2}
        
        # 为每个检测头创建网络层
        for head in self.heads:
            classes = self.heads[head]
            if head_conv > 0:
                # 使用中间卷积层
                fc = nn.Sequential(
                    nn.Conv2d(
                        in_channels,
                        head_conv,
                        kernel_size=3,
                        padding=1,
                        bias=True), 
                    nn.ReLU(inplace=True),
                    nn.Conv2d(
                        head_conv,
                        classes,
                        kernel_size=1,
                        stride=1,
                        padding=0,
                        bias=True))
                
                # 特殊的初始化策略
                if 'hm' in head:
                    # 热力图头使用特殊的偏置初始化
                    fc[-1].bias.data.fill_(-2.19)
                else:
                    # 回归头使用标准初始化
                    fill_fc_weights(fc)
            else:
                # 直接使用单层卷积
                fc = nn.Conv2d(
                    in_channels,
                    classes,
                    kernel_size=1,
                    stride=1,
                    padding=0,
                    bias=True)
                
                # 特殊的初始化策略
                if 'hm' in head:
                    fc.bias.data.fill_(-2.19)
                else:
                    fill_fc_weights(fc)
            
            # 动态设置属性
            self.__setattr__(head, fc)

    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入特征图 [B, C, H, W]
            
        Returns:
            dict: 包含各个检测头输出的字典
                - hm: 双通道热力图 [B, 2, H, W]
                - reg: 亚像素偏移 [B, 2, H, W]
                - c2v: 中心到顶点 [B, 8, H, W]
                - v2c: 顶点到中心 [B, 8, H, W]
        """
        ret = {}
        for head in self.heads:
            ret[head] = self.__getattr__(head)(x)
        return ret


class DLASegMS(nn.Module):
    """
    DLA 分割网络 (ModelScope 版本)
    
    集成 DLA 骨干网络、上采样模块和检测头，
    严格遵循 ModelScope 原始实现。
    """

    def __init__(self,
                 base_name='dla34',
                 pretrained=False,
                 down_ratio=4,
                 head_conv=256):
        """
        初始化 DLA 分割网络
        
        Args:
            base_name: 骨干网络名称
            pretrained: 是否使用预训练权重
            down_ratio: 下采样比例
            head_conv: 检测头中间层通道数
        """
        super(DLASegMS, self).__init__()
        
        # 导入 DLA 骨干网络
        from .dla_backbone_ms import dla34, DLAUp
        import numpy as np
        
        assert down_ratio in [2, 4, 8, 16]
        
        # 检测头配置
        self.heads = {'hm': 2, 'v2c': 8, 'c2v': 8, 'reg': 2}
        self.first_level = int(np.log2(down_ratio))
        
        # 创建骨干网络
        self.base = dla34(pretrained=pretrained, return_levels=True)
        channels = self.base.channels
        
        # 创建上采样模块
        scales = [2**i for i in range(len(channels[self.first_level:]))]
        self.dla_up = DLAUp(channels[self.first_level:], scales=scales)

        # 创建检测头
        for head in self.heads:
            classes = self.heads[head]
            if head_conv > 0:
                fc = nn.Sequential(
                    nn.Conv2d(
                        channels[self.first_level],
                        head_conv,
                        kernel_size=3,
                        padding=1,
                        bias=True), nn.ReLU(inplace=True),
                    nn.Conv2d(
                        head_conv,
                        classes,
                        kernel_size=1,
                        stride=1,
                        padding=0,
                        bias=True))
                if 'hm' in head:
                    fc[-1].bias.data.fill_(-2.19)
                else:
                    fill_fc_weights(fc)
            else:
                fc = nn.Conv2d(
                    channels[self.first_level],
                    classes,
                    kernel_size=1,
                    stride=1,
                    padding=0,
                    bias=True)
                if 'hm' in head:
                    fc.bias.data.fill_(-2.19)
                else:
                    fill_fc_weights(fc)
            self.__setattr__(head, fc)

    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入图像张量 [B, 3, H, W]
            
        Returns:
            list: 包含检测结果字典的列表
        """
        x = self.base(x)
        x = self.dla_up(x[self.first_level:])
        ret = {}
        for head in self.heads:
            output = self.__getattr__(head)(x)
            # 对热图输出应用 sigmoid 激活，确保值在 [0, 1] 范围内
            if head == 'hm':
                output = torch.sigmoid(output)
            ret[head] = output
        return [ret]


def TableRecModelMS():
    """
    创建表格识别模型 (ModelScope 版本)
    与 ModelScope 原始实现完全一致
    """
    model = DLASegMS()
    return model
