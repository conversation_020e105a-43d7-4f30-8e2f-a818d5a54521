# Time: 2025-07-13
# Author: <EMAIL>
# FileName: cycle_centernet_loss_ms.py

"""
Cycle-CenterNet 损失函数 (ModelScope 版本)

基于现有的损失函数实现，适配 ModelScope 版本的双通道热力图。
复用 train-anything 现有的损失函数结构，扩展支持双通道热力图计算。

关键特性:
1. 双通道热力图损失计算
   - 第1通道: 边界框中心点检测损失
   - 第2通道: 顶点检测损失
2. 复用现有的 L1Loss 用于回归头
3. 与 ModelScope 完全一致的损失计算逻辑
4. 支持动态权重计算
"""

import math
from typing import Dict, Any, Optional, Tuple, List

import torch
import torch.nn as nn
import torch.nn.functional as F

# 复用现有的损失函数
from ..cycle_centernet.cycle_centernet_loss import (
    gaussian_focal_loss,
    L1Loss,
    compute_pairing_weight
)


class GaussianFocalLossMS(nn.Module):
    """
    Gaussian Focal Loss (ModelScope 版本)
    
    扩展现有的 GaussianFocalLoss 以支持双通道热力图计算。
    第1通道用于边界框中心点检测，第2通道用于顶点检测。
    
    Args:
        alpha: 正样本调制因子
        gamma: 负样本调制因子
        reduction: 损失归约方式
        loss_weight: 损失权重
    """
    
    def __init__(
        self,
        alpha: float = 2.0,
        gamma: float = 4.0,
        reduction: str = 'mean',
        loss_weight: float = 1.0
    ):
        super(GaussianFocalLossMS, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        self.loss_weight = loss_weight
    
    def forward(
        self,
        pred: torch.Tensor,
        target: torch.Tensor,
        weight: Optional[torch.Tensor] = None,
        avg_factor: Optional[int] = None,
        reduction_override: Optional[str] = None
    ) -> torch.Tensor:
        """
        前向传播计算双通道热力图损失
        
        Args:
            pred: 预测热图，形状为 (N, 2, H, W) - 双通道
            target: 目标热图，形状为 (N, 2, H, W) - 双通道
            weight: 样本权重，可选
            avg_factor: 平均因子，用于归一化
            reduction_override: 覆盖默认的归约方式
            
        Returns:
            损失值
        """
        assert reduction_override in (None, 'none', 'mean', 'sum')
        reduction = reduction_override if reduction_override else self.reduction
        
        # 确保输入是双通道
        assert pred.size(1) == 2, f"期望双通道输入，但得到 {pred.size(1)} 通道"
        assert target.size(1) == 2, f"期望双通道目标，但得到 {target.size(1)} 通道"
        
        # 分别计算两个通道的损失
        loss_channel_0 = gaussian_focal_loss(
            pred=pred[:, 0:1, :, :],  # 第1通道：边界框中心点
            target=target[:, 0:1, :, :],
            alpha=self.alpha,
            gamma=self.gamma
        )
        
        loss_channel_1 = gaussian_focal_loss(
            pred=pred[:, 1:2, :, :],  # 第2通道：顶点
            target=target[:, 1:2, :, :],
            alpha=self.alpha,
            gamma=self.gamma
        )
        
        # 合并两个通道的损失
        loss = loss_channel_0 + loss_channel_1
        
        # 应用样本权重
        if weight is not None:
            loss = loss * weight
        
        # 应用归约
        if reduction == 'mean':
            if avg_factor is not None:
                loss = loss.sum() / avg_factor
            else:
                loss = loss.mean()
        elif reduction == 'sum':
            loss = loss.sum()
        # reduction == 'none' 时不做处理
        
        return self.loss_weight * loss


class CycleCenterNetLossMS(nn.Module):
    """
    Cycle-CenterNet 组合损失函数 (ModelScope 版本)
    
    适配 ModelScope 版本的双通道热力图和回归头输出。
    复用现有的损失函数结构，确保与 train-anything 框架兼容。
    
    损失组成:
    - L_hm: 双通道热力图损失 (GaussianFocalLossMS)
    - L_reg: 亚像素偏移损失 (L1Loss)
    - L_c2v: 中心到顶点损失 (L1Loss)
    - L_v2c: 顶点到中心损失 (L1Loss)
    
    Args:
        heatmap_loss_cfg: 热力图损失配置
        reg_loss_cfg: 回归损失配置
        c2v_loss_cfg: 中心到顶点损失配置
        v2c_loss_cfg: 顶点到中心损失配置
    """

    def __init__(
        self,
        heatmap_loss_cfg: Dict[str, Any] = None,
        reg_loss_cfg: Dict[str, Any] = None,
        c2v_loss_cfg: Dict[str, Any] = None,
        v2c_loss_cfg: Dict[str, Any] = None
    ):
        super(CycleCenterNetLossMS, self).__init__()

        # 双通道热力图损失
        if heatmap_loss_cfg is None:
            heatmap_loss_cfg = dict(alpha=2.0, gamma=4.0, loss_weight=1.0)
        self.heatmap_loss = GaussianFocalLossMS(**heatmap_loss_cfg)

        # 亚像素偏移损失 (reg)
        if reg_loss_cfg is None:
            reg_loss_cfg = dict(loss_weight=1.0)
        self.reg_loss = L1Loss(**reg_loss_cfg)

        # 中心到顶点损失 (c2v)
        if c2v_loss_cfg is None:
            c2v_loss_cfg = dict(loss_weight=1.0)
        self.c2v_loss = L1Loss(**c2v_loss_cfg)

        # 顶点到中心损失 (v2c)
        if v2c_loss_cfg is None:
            v2c_loss_cfg = dict(loss_weight=0.5)
        self.v2c_loss = L1Loss(**v2c_loss_cfg)

    def forward(
        self,
        predictions: List[Dict[str, torch.Tensor]],
        targets: Dict[str, torch.Tensor],
        avg_factor: Optional[int] = None
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播计算组合损失
        
        Args:
            predictions: 模型预测结果列表，每个元素是包含 hm, reg, c2v, v2c 的字典
            targets: 目标字典，包含各种损失的目标值
            avg_factor: 平均因子
            
        Returns:
            损失字典
        """
        losses = {}
        
        # 获取预测结果（ModelScope 输出格式）
        pred_dict = predictions[0]  # 取第一个（也是唯一一个）预测结果
        
        # 1. 双通道热力图损失
        if 'hm' in pred_dict and 'heatmap_target' in targets:
            hm_pred = pred_dict['hm']  # [B, 2, H, W]
            hm_target = targets['heatmap_target']  # [B, 2, H, W]
            
            loss_heatmap = self.heatmap_loss(
                pred=hm_pred,
                target=hm_target,
                avg_factor=avg_factor
            )
            losses['loss_heatmap'] = loss_heatmap
        
        # 2. 亚像素偏移损失 (reg)
        if 'reg' in pred_dict and 'offset_target' in targets:
            reg_pred = pred_dict['reg']  # [B, 2, H, W]
            reg_target = targets['offset_target']  # [B, 2, H, W]
            reg_weight = targets.get('offset_target_weight', None)

            loss_offset = self.reg_loss(
                pred=reg_pred,
                target=reg_target,
                weight=reg_weight,
                avg_factor=avg_factor * 2 if avg_factor else None  # 2个通道
            )
            losses['loss_offset'] = loss_offset

        # 3. 中心到顶点损失 (c2v)
        if 'c2v' in pred_dict and 'center2vertex_target' in targets:
            c2v_pred = pred_dict['c2v']  # [B, 8, H, W]
            c2v_target = targets['center2vertex_target']  # [B, 8, H, W]

            # 计算动态权重（如果提供了 v2c 数据）
            pairing_weight = targets.get('pairing_weight', None)
            if pairing_weight is None and 'v2c' in pred_dict and 'vertex2center_target' in targets:
                # 动态计算权重
                pairing_weight = self._compute_pairing_weight(
                    c2v_pred, c2v_target,
                    pred_dict['v2c'], targets['vertex2center_target']
                )

            loss_c2v = self.c2v_loss(
                pred=c2v_pred,
                target=c2v_target,
                weight=pairing_weight,
                avg_factor=avg_factor * 8 if avg_factor else None  # 8个通道
            )
            losses['loss_c2v'] = loss_c2v

        # 4. 顶点到中心损失 (v2c)
        if 'v2c' in pred_dict and 'vertex2center_target' in targets:
            v2c_pred = pred_dict['v2c']  # [B, 8, H, W]
            v2c_target = targets['vertex2center_target']  # [B, 8, H, W]

            # 使用相同的动态权重
            pairing_weight = targets.get('pairing_weight', None)
            if pairing_weight is None and 'c2v' in pred_dict and 'center2vertex_target' in targets:
                pairing_weight = self._compute_pairing_weight(
                    pred_dict['c2v'], targets['center2vertex_target'],
                    v2c_pred, v2c_target
                )

            loss_v2c = self.v2c_loss(
                pred=v2c_pred,
                target=v2c_target,
                weight=pairing_weight,
                avg_factor=avg_factor * 8 if avg_factor else None  # 8个通道
            )
            losses['loss_v2c'] = loss_v2c
        
        return losses

    def _compute_pairing_weight(
        self,
        c2v_pred: torch.Tensor,
        c2v_target: torch.Tensor,
        v2c_pred: torch.Tensor,
        v2c_target: torch.Tensor
    ) -> torch.Tensor:
        """
        计算动态配对权重
        复用现有的权重计算逻辑
        """
        # 计算预测误差
        c2v_error = torch.abs(c2v_pred - c2v_target)
        v2c_error = torch.abs(v2c_pred - v2c_target)

        # 计算归一化误差 D_cv
        c2v_norm = torch.abs(c2v_target) + 1e-8
        D_cv = torch.clamp((c2v_error + v2c_error) / c2v_norm, max=1.0)

        # 计算动态权重：ω(P_cv) = 1 - exp(-π * D_cv)
        pi = math.pi
        pairing_weight = 1.0 - torch.exp(-pi * D_cv)

        # 只在有目标值的位置应用权重
        valid_mask = (c2v_target != 0) | (v2c_target != 0)
        pairing_weight = pairing_weight * valid_mask.float()

        return pairing_weight


def create_cycle_centernet_ms_loss(
    heatmap_loss_weight: float = 1.0,
    reg_loss_weight: float = 1.0,
    c2v_loss_weight: float = 1.0,
    v2c_loss_weight: float = 0.5
) -> CycleCenterNetLossMS:
    """
    创建 Cycle-CenterNet 损失函数 (ModelScope 版本)
    
    Args:
        heatmap_loss_weight: 热力图损失权重
        reg_loss_weight: 回归损失权重
        c2v_loss_weight: 中心到顶点损失权重
        v2c_loss_weight: 顶点到中心损失权重
        
    Returns:
        损失函数实例
    """
    heatmap_loss_cfg = dict(
        alpha=2.0,
        gamma=4.0,
        loss_weight=heatmap_loss_weight
    )
    
    reg_loss_cfg = dict(loss_weight=reg_loss_weight)
    c2v_loss_cfg = dict(loss_weight=c2v_loss_weight)
    v2c_loss_cfg = dict(loss_weight=v2c_loss_weight)

    return CycleCenterNetLossMS(
        heatmap_loss_cfg=heatmap_loss_cfg,
        reg_loss_cfg=reg_loss_cfg,
        c2v_loss_cfg=c2v_loss_cfg,
        v2c_loss_cfg=v2c_loss_cfg
    )
