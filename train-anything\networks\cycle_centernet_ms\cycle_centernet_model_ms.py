# Time: 2025-07-13
# Author: <EMAIL>
# FileName: cycle_centernet_model_ms.py

"""
Cycle-CenterNet 完整模型 (ModelScope 版本)

将 DLA-34 骨干网络和双通道检测头组装成完整的模型。
严格遵循 ModelScope 原始实现的架构和接口。

关键特性:
1. 集成 DLA-34 骨干网络和双通道检测头
2. 支持加载 ModelScope 预训练权重
3. 与 ModelScope 完全一致的前向传播流程
4. 支持训练和推理模式
"""

import os
from collections import OrderedDict
from typing import Dict, Any, Optional

import torch
import torch.nn as nn

from .dla_backbone_ms import DLA34BackboneMS
from .cycle_centernet_head_ms import DLASegMS


class CycleCenterNetModelMS(nn.Module):
    """
    Cycle-CenterNet 完整模型 (ModelScope 版本)
    
    这是完整的 Cycle-CenterNet 模型实现，集成了：
    1. DLA-34 骨干网络
    2. DLA 上采样模块
    3. 双通道检测头
    
    与 ModelScope 原始实现保持完全一致。
    """

    def __init__(self, 
                 base_name='dla34',
                 pretrained=False,
                 down_ratio=4,
                 head_conv=256):
        """
        初始化 Cycle-CenterNet 模型
        
        Args:
            base_name: 骨干网络名称 (默认 'dla34')
            pretrained: 是否使用预训练权重
            down_ratio: 下采样比例 (默认 4)
            head_conv: 检测头中间层通道数 (默认 256)
        """
        super(CycleCenterNetModelMS, self).__init__()
        
        # 使用 DLASegMS 作为核心模型
        self.model = DLASegMS(
            base_name=base_name,
            pretrained=pretrained,
            down_ratio=down_ratio,
            head_conv=head_conv
        )
        
        # 保存配置参数
        self.base_name = base_name
        self.down_ratio = down_ratio
        self.head_conv = head_conv

    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入图像张量 [B, 3, H, W]
            
        Returns:
            list: 包含检测结果字典的列表
                [{'hm': [B, 2, H/4, W/4],
                  'reg': [B, 2, H/4, W/4],
                  'c2v': [B, 8, H/4, W/4],
                  'v2c': [B, 8, H/4, W/4]}]
        """
        return self.model(x)

    def load_modelscope_weights(self, checkpoint_path: str):
        """
        加载 ModelScope 预训练权重
        
        Args:
            checkpoint_path: ModelScope 权重文件路径
        """
        if not os.path.exists(checkpoint_path):
            print(f"警告: 权重文件不存在: {checkpoint_path}")
            return
            
        try:
            checkpoint = torch.load(checkpoint_path, map_location='cpu')
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            else:
                state_dict = checkpoint


            if list(state_dict.keys())[0].startswith('base'):
                new_state_dict = OrderedDict()
                for k, v in state_dict.items():
                    new_state_dict[f"model.{k}"] = v
                state_dict = new_state_dict
                
            # 直接加载权重，因为我们的模型结构与 ModelScope 一致
            missing_keys, unexpected_keys = self.load_state_dict(state_dict, strict=True)
            
            print(f"成功加载 ModelScope 权重: {checkpoint_path}")
            if missing_keys:
                print(f"缺失的键: {missing_keys[:10]}...")  # 只显示前10个
            if unexpected_keys:
                print(f"意外的键: {unexpected_keys[:10]}...")  # 只显示前10个
                
        except Exception as e:
            print(f"加载权重失败: {e}")

    def get_model_info(self):
        """
        获取模型信息
        
        Returns:
            dict: 模型信息字典
        """
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'model_name': 'CycleCenterNetModelMS',
            'base_name': self.base_name,
            'down_ratio': self.down_ratio,
            'head_conv': self.head_conv,
            'total_params': total_params,
            'trainable_params': trainable_params,
            'model_size_mb': total_params * 4 / 1024 / 1024  # 假设 float32
        }

    def set_train_mode(self, mode=True):
        """
        设置训练模式
        
        Args:
            mode: True 为训练模式，False 为评估模式
        """
        self.train(mode)
        return self

    def set_eval_mode(self):
        """
        设置评估模式
        """
        return self.set_train_mode(False)


def create_cycle_centernet_ms_model(config=None):
    """
    创建 Cycle-CenterNet 模型 (ModelScope 版本)
    
    Args:
        config: 模型配置参数，包含以下可选字段：
            - base_name: 骨干网络名称 (默认 'dla34')
            - pretrained: 是否使用预训练权重 (默认 False)
            - down_ratio: 下采样比例 (默认 4)
            - head_conv: 检测头中间层通道数 (默认 256)
            - checkpoint_path: ModelScope 权重文件路径 (可选)
            
    Returns:
        CycleCenterNetModelMS: 模型实例
    """
    # 默认配置
    default_config = {
        'base_name': 'dla34',
        'pretrained': False,
        'down_ratio': 4,
        'head_conv': 256
    }
    
    # 合并配置
    if config is not None:
        default_config.update(config)
    
    # 创建模型
    model = CycleCenterNetModelMS(
        base_name=default_config['base_name'],
        pretrained=default_config['pretrained'],
        down_ratio=default_config['down_ratio'],
        head_conv=default_config['head_conv']
    )
    
    # 如果提供了权重路径，则加载权重
    if config and 'checkpoint_path' in config and config['checkpoint_path']:
        model.load_modelscope_weights(config['checkpoint_path'])
    
    return model


# 为了与 ModelScope 原始接口保持一致
def TableRecModelMS():
    """
    创建表格识别模型 (ModelScope 版本)
    与 ModelScope 原始实现完全一致的接口
    """
    return create_cycle_centernet_ms_model()


# 别名，保持向后兼容
CycleCenterNetMS = CycleCenterNetModelMS
