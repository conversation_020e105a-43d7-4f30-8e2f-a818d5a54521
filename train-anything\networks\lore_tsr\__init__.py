#!/usr/bin/env python3
"""
LORE-TSR 网络模块初始化文件

迁移1：基础导出接口和版本信息
后续迭代将逐步添加具体组件导出

Time: 2025-07-18
Author: LORE-TSR Migration Team
Description: LORE-TSR 表格结构识别网络模块，从原LORE-TSR项目迁移到train-anything框架
"""

# 版本信息
__version__ = "1.0.0"
__author__ = "LORE-TSR Migration Team"
__description__ = "LORE-TSR 表格结构识别网络模块"

# 迭代1：基础导出接口（空实现占位）
# 后续迭代将逐步取消注释并实现

# 迭代2：模型相关导出
from .lore_tsr_model import create_lore_tsr_model, LoreTsrModel
from .backbones import BACKBONE_FACTORY, get_backbone_factory, create_backbone  # 迭代2步骤2.2实现
from .heads import validate_heads_config, analyze_heads_config, print_heads_summary  # 迭代2步骤2.3实现

# 迭代4：损失函数导出
# from .lore_tsr_loss import LoreTsrLoss

# 迭代6：处理器导出
# from .processor import Processor
# from .transformer import Transformer

# 当前导出列表（迭代2步骤2.3）
__all__ = [
    "__version__",
    "__author__",
    "__description__",
    # 迭代2步骤2.1：模型工厂函数
    "create_lore_tsr_model",
    "LoreTsrModel",
    # 迭代2步骤2.2：骨干网络工厂
    "BACKBONE_FACTORY",
    "get_backbone_factory",
    "create_backbone",
    # 迭代2步骤2.3：检测头工具函数
    "validate_heads_config",
    "analyze_heads_config",
    "print_heads_summary",
    # 后续迭代将逐步添加：
    # "LoreTsrLoss",
    # "Processor",
    # "Transformer",
]

# 迭代1：模块初始化检查
def _check_dependencies():
    """检查必要的依赖是否可用"""
    try:
        import torch
        import torchvision
        return True
    except ImportError as e:
        print(f"警告：缺少必要依赖 {e}")
        return False

# 执行依赖检查
_dependencies_ok = _check_dependencies()

if not _dependencies_ok:
    print("警告：LORE-TSR模块依赖检查失败，某些功能可能不可用")
