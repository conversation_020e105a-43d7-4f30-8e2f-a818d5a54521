#!/usr/bin/env python3
"""
LORE-TSR 检测头模块初始化文件

管理LORE-TSR的多任务检测头实现

Time: 2025-07-18
Author: LORE-TSR Migration Team
Description: 检测头模块，包含多任务输出头的实现
"""

# 版本信息
__version__ = "1.0.0"

# 迭代1：基础导出接口（空实现占位）
# 后续迭代将逐步取消注释并实现

# 迭代2步骤2.3：检测头工具函数导出
from .lore_tsr_head import (
    validate_heads_config,
    get_head_info,
    get_all_heads_info,
    analyze_heads_config,
    print_heads_summary,
    LoreTsrHeadInterface,
    create_head_interface,
    LORE_TSR_HEAD_INFO,
)

__all__ = [
    "__version__",
    # 迭代2步骤2.3：检测头工具函数
    "validate_heads_config",
    "get_head_info",
    "get_all_heads_info",
    "analyze_heads_config",
    "print_heads_summary",
    "LoreTsrHeadInterface",
    "create_head_interface",
    "LORE_TSR_HEAD_INFO",
]
