#!/usr/bin/env python3
"""
LORE-TSR 检测头工具和接口层
轻量级工具函数，保持原有设计的简洁性

设计理念：
- 保持LORE-TSR原有的配置传递模式：配置文件 -> 模型工厂 -> backbone
- 检测头仍嵌入在骨干网络中，不改变现有实现
- 提供工具函数用于配置验证、信息查询和调试
- 为后续迭代的检测头分离预留清晰接口

Time: 2025-07-19
Author: LORE-TSR Migration Team
Description: 轻量级检测头工具函数，避免过度抽象
"""

from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

# LORE-TSR检测头信息（用于文档和调试）
LORE_TSR_HEAD_INFO = {
    'hm': {
        'description': '热力图 [B, 2, H//4, W//4] - 背景+单元格中心点',
        'typical_channels': 2,
        'purpose': '检测单元格中心点位置'
    },
    'wh': {
        'description': '边界框 [B, 8, H//4, W//4] - 4个角点坐标',
        'typical_channels': 8,
        'purpose': '回归单元格的4个角点坐标'
    },
    'reg': {
        'description': '偏移 [B, 2, H//4, W//4] - 中心点亚像素偏移',
        'typical_channels': 2,
        'purpose': '中心点的亚像素级偏移修正'
    },
    'st': {
        'description': '结构 [B, 8, H//4, W//4] - 表格结构信息',
        'typical_channels': 8,
        'purpose': '表格结构和单元格关系信息'
    },
    'ax': {
        'description': '轴向特征 [B, 256, H//4, W//4] - 逻辑位置特征',
        'typical_channels': 256,
        'purpose': '逻辑位置和轴向关系特征'
    },
    'cr': {
        'description': '角点特征 [B, 256, H//4, W//4] - 角点回归特征',
        'typical_channels': 256,
        'purpose': '角点检测和回归的深层特征'
    }
}

def validate_heads_config(heads: Dict[str, int]) -> bool:
    """
    验证检测头配置的有效性

    Args:
        heads: 检测头配置字典，格式如 {'hm': 2, 'wh': 8, 'reg': 2, ...}

    Returns:
        bool: 配置是否有效
    """
    if not isinstance(heads, dict):
        logger.error("检测头配置必须是字典类型")
        return False

    # 检查必需的检测头
    required_heads = {'hm', 'wh'}  # 最基本的检测头
    missing_heads = required_heads - heads.keys()
    if missing_heads:
        logger.warning(f"缺少必需的检测头: {missing_heads}")
        return False

    # 检查通道数的合理性
    for head, channels in heads.items():
        if not isinstance(channels, int) or channels <= 0:
            logger.error(f"检测头 {head} 的通道数无效: {channels}")
            return False

        # 检查是否与典型配置差异过大
        if head in LORE_TSR_HEAD_INFO:
            typical = LORE_TSR_HEAD_INFO[head]['typical_channels']
            if channels != typical:
                logger.info(f"检测头 {head} 通道数 {channels} 与典型配置 {typical} 不同")

    return True

def get_head_info(head_name: str) -> Dict[str, Any]:
    """
    获取检测头的详细信息

    Args:
        head_name: 检测头名称

    Returns:
        Dict[str, Any]: 检测头信息，包含描述、通道数、用途等
    """
    return LORE_TSR_HEAD_INFO.get(head_name, {
        'description': f'未知检测头: {head_name}',
        'typical_channels': 'unknown',
        'purpose': '未定义'
    })

def get_all_heads_info() -> Dict[str, Dict[str, Any]]:
    """
    获取所有检测头的信息

    Returns:
        Dict[str, Dict[str, Any]]: 所有检测头的详细信息
    """
    return LORE_TSR_HEAD_INFO.copy()

def analyze_heads_config(heads: Dict[str, int]) -> Dict[str, Any]:
    """
    分析检测头配置，提供详细的配置报告

    Args:
        heads: 检测头配置字典

    Returns:
        Dict[str, Any]: 配置分析报告
    """
    analysis = {
        'valid': validate_heads_config(heads),
        'total_heads': len(heads),
        'total_channels': sum(heads.values()),
        'heads_detail': {},
        'warnings': [],
        'recommendations': []
    }

    for head, channels in heads.items():
        head_info = get_head_info(head)
        analysis['heads_detail'][head] = {
            'channels': channels,
            'description': head_info['description'],
            'purpose': head_info['purpose']
        }

        # 检查配置合理性
        if head in LORE_TSR_HEAD_INFO:
            typical = LORE_TSR_HEAD_INFO[head]['typical_channels']
            if channels != typical:
                analysis['warnings'].append(
                    f"{head}: 配置{channels}通道，典型配置为{typical}通道"
                )

    # 提供建议
    if 'reg' not in heads:
        analysis['recommendations'].append("建议添加'reg'检测头用于亚像素偏移修正")

    if analysis['total_channels'] > 1000:
        analysis['warnings'].append("总通道数较大，可能影响训练效率")

    return analysis

def print_heads_summary(heads: Dict[str, int]):
    """
    打印检测头配置的摘要信息

    Args:
        heads: 检测头配置字典
    """
    print("=" * 60)
    print("LORE-TSR 检测头配置摘要")
    print("=" * 60)

    analysis = analyze_heads_config(heads)

    print(f"配置有效性: {'✅ 有效' if analysis['valid'] else '❌ 无效'}")
    print(f"检测头数量: {analysis['total_heads']}")
    print(f"总输出通道: {analysis['total_channels']}")
    print()

    print("检测头详情:")
    print("-" * 60)
    for head, details in analysis['heads_detail'].items():
        print(f"{head:>4}: {details['channels']:>3}通道 - {details['purpose']}")

    if analysis['warnings']:
        print("\n⚠️  警告:")
        for warning in analysis['warnings']:
            print(f"  - {warning}")

    if analysis['recommendations']:
        print("\n💡 建议:")
        for rec in analysis['recommendations']:
            print(f"  - {rec}")

    print("=" * 60)

# 为后续迭代预留的接口
class LoreTsrHeadInterface:
    """
    LORE-TSR检测头接口定义
    为后续迭代的检测头分离预留的抽象接口
    """

    def __init__(self, heads: Dict[str, int], head_conv: int):
        """
        初始化检测头接口

        Args:
            heads: 检测头配置
            head_conv: 中间层通道数
        """
        self.heads = heads
        self.head_conv = head_conv
        self.validate()

    def validate(self):
        """验证配置"""
        if not validate_heads_config(self.heads):
            raise ValueError("检测头配置无效")

    def get_config(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            'heads': self.heads.copy(),
            'head_conv': self.head_conv,
            'analysis': analyze_heads_config(self.heads)
        }

    def summary(self):
        """打印配置摘要"""
        print_heads_summary(self.heads)

def create_head_interface(heads: Dict[str, int], head_conv: int = 64) -> LoreTsrHeadInterface:
    """
    创建检测头接口实例

    Args:
        heads: 检测头配置
        head_conv: 中间层通道数

    Returns:
        LoreTsrHeadInterface: 检测头接口实例
    """
    return LoreTsrHeadInterface(heads, head_conv)
