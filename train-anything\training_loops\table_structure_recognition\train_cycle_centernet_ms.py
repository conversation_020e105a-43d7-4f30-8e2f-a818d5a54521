#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-07-13
# <AUTHOR> <EMAIL>
# @FileName: train_cycle_centernet_ms.py

"""
Cycle-CenterNet 表格结构识别训练脚本 (ModelScope 版本)

基于 accelerate 框架的分布式训练实现，使用 ModelScope 版本的 Cycle-CenterNet 模型。
严格遵循 ModelScope 的双通道热力图和推理流程。
"""

import os
import math
import json
import random
import datetime
import warnings
from pathlib import Path
from itertools import islice
from typing import Tuple, List

# 设置环境变量解决DDP未使用参数问题 - 必须在导入torch之前设置
os.environ['TORCH_DISTRIBUTED_DEBUG'] = 'DETAIL'

import torch
import torch.nn as nn
import torch.utils.data
from tqdm import tqdm

from accelerate.logging import get_logger
import modules.utils.torch_utils as torch_utils
from modules.utils.log import create_file_logger
from modules.utils.torch_utils import EMAHandler
from modules.utils.optimization import get_scheduler
from modules.proj_cmd_args.cycle_centernet.args import parse_args
from modules.utils.train_utils import prepare_training_enviornment_v2
from modules.utils.train_tools import (
    save_state,
    get_optimizer,
    get_random_states,
    set_random_states,
    walk_dataloaders,
    find_latest_checkpoint,
)

from networks.cycle_centernet_ms import (
    create_cycle_centernet_ms_loss,
    create_cycle_centernet_ms_model
)
from my_datasets.table_structure_recognition import (
    collate_fn,
    TableDataset,
    prepare_targets,
)

# 使用新的配置系统
config = parse_args()
logger = get_logger(__name__)
file_logger_path = Path(os.path.join(config.basic.output_dir, "logs", "monitors.log"))
os.makedirs(file_logger_path.parent, exist_ok=True)
file_logger = create_file_logger(file_logger_path, "DEBUG")
torch.autograd.set_detect_anomaly(True)

# 忽略特定警告
warnings.filterwarnings("ignore", message="torch.meshgrid", category=UserWarning)
warnings.filterwarnings("ignore", message="Grad strides do not match bucket view strides", category=UserWarning)
warnings.filterwarnings("ignore", message="On January 1, 2023, MMCV will release v2.0.0", category=UserWarning, )


def prepare_dataloaders(config, mode: str, train_batch_size_per_device: int, seed: int = -1):
    """
    准备数据加载器

    Args:
        config: 配置对象
        mode: 模式 ('train', 'val')
        train_batch_size_per_device: 每设备批次大小
        seed: 随机种子

    Returns:
        数据集和数据加载器列表
    """
    debug = config.basic.debug
    if debug:
        msg = f"Using debug mode to load {mode} dataset with small size"
        logger.info(msg)
        file_logger.info(msg)

    datasets: List[TableDataset] = []
    loaders: List[Tuple[str, torch.utils.data.DataLoader]] = []

    # 确定数据目录
    if mode == "train":
        data_dir = config.data.paths.train_data_dir
    elif mode == "val":
        data_dir = config.data.paths.val_data_dir
    else:
        raise ValueError(f"Unsupported mode: {mode}")

    if data_dir is None:
        logger.warning(f"No {mode} data directory provided")
        return datasets, loaders

    # 创建数据集（内部自动实例化TableTransforms）
    dataset = TableDataset(
        mode=mode,
        seed=seed,
        debug=debug,
        data_root=data_dir,
        max_samples=config.data.processing.max_samples if debug else None,
        target_size=tuple(config.data.processing.image_size),
        mean=config.data.processing.normalize.mean,
        std=config.data.processing.normalize.std,
        to_rgb=config.data.processing.normalize.to_rgb,
        apply_transforms=True  # 启用变换
    )
    datasets.append(dataset)

    # 创建数据加载器
    dataloader = torch.utils.data.DataLoader(
        dataset,
        shuffle=(mode == "train"),
        batch_size=train_batch_size_per_device,
        num_workers=config.data.loader.num_workers,
        pin_memory=config.data.loader.pin_memory,
        collate_fn=collate_fn
    )
    
    loaders.append((f"Table{mode.capitalize()}", dataloader))
    
    return datasets, loaders


def save_best_checkpoints(
    config,
    accelerator,
    model,
    ema_handler,
    optimizer,
    lr_scheduler,
    global_step,
    val_metrics,
    record_dump_path,
    current_best_record
):
    """
    保存最佳模型检查点

    Args:
        config: 参数
        accelerator: accelerate对象
        model: 模型
        ema_handler: EMA处理器
        optimizer: 优化器
        lr_scheduler: 学习率调度器
        global_step: 全局步数
        val_metrics: 验证指标
        record_dump_path: 记录文件路径
        current_best_record: 当前最佳记录
    """
    found_best_model = False
    avg_loss = val_metrics
    tmp_record = {"avg_loss": avg_loss}

    # 损失越小越好
    if avg_loss <= current_best_record["avg_loss"]:
        found_best_model = True

    if found_best_model:
        current_best_record.update(tmp_record)
        with open(record_dump_path, 'w', encoding='utf-8') as f:
            data = dict()
            data.update(current_best_record)
            data['checkpoint_step'] = global_step
            json.dump(data, f, ensure_ascii=False, indent=4)

        msg = f"Found best model: {data}"
        logger.info(msg)
        file_logger.info(msg)

        save_state(
            Path(record_dump_path).parent,
            model,
            optimizer,
            lr_scheduler,
            accelerator,
            ema_handler=ema_handler,
            global_step=global_step,
            epoch=None,          # 最佳模型不需要epoch信息
            random_states=None,  # 最佳模型不需要随机状态
            logger=logger,
            file_logger=file_logger,
        )


def log_validation(
    config,
    model: nn.Module,
    ema_handler,
    global_step: int,
    accelerator,
    weight_dtype: torch.dtype,
    val_loaders: List[Tuple[str, torch.utils.data.DataLoader]]
) -> float:
    """
    验证日志记录，对齐到Lama的实现

    Args:
        config: 参数
        model: 模型
        ema_handler: EMA处理器
        global_step: 全局步数
        accelerator: accelerate对象
        weight_dtype: 权重数据类型
        val_loaders: 验证数据加载器列表

    Returns:
        验证指标
    """
    if not val_loaders:
        return float('inf')  # 返回无穷大表示没有验证数据

    model = accelerator.unwrap_model(model)
    if config.ema.enabled and ema_handler is not None:
        ema_handler.store(model)
        ema_handler.apply_to(model)
    model.eval()

    print()
    msg = f"Running metrics for step-{global_step}..."
    logger.info(msg)
    file_logger.info(msg)

    # 创建损失函数 (ModelScope 版本)
    criterion = create_cycle_centernet_ms_loss()

    total_loss = 0.0
    total_samples = 0

    # TODO: 验证评估会比较耗时, 可以指定数量
    if config.checkpoint.validation.num_batches is not None and isinstance(config.checkpoint.validation.num_batches, int):
        num_batch_to_eval = config.checkpoint.validation.num_batches
        test_pbar = tqdm(total=num_batch_to_eval, desc='evaluating...')
        dataloader_steps = islice(walk_dataloaders(val_loaders, logger=logger), 0, num_batch_to_eval)
    else:
        test_dataloader_total_steps = sum(len(loader) for flag, loader in val_loaders)
        test_pbar = tqdm(total=test_dataloader_total_steps, desc='evaluating...')
        dataloader_steps = walk_dataloaders(val_loaders, logger=logger)

    with torch.no_grad():
        for step, (flag, batch_data) in enumerate(dataloader_steps):
            images = batch_data['images'].to(accelerator.device, dtype=weight_dtype)

            # 前向传播
            predictions = model(images)

            # 准备目标 - 修复：predictions[0] 是字典，需要从 'hm' 键获取热图张量
            heatmap_tensor = predictions[0]['hm']  # [B, 2, H, W]
            output_size = heatmap_tensor.shape[2:]  # (H, W)
            heatmap_channels = getattr(config.data.processing, 'heatmap_channels', 2)  # 默认双通道
            targets = prepare_targets(batch_data, output_size, heatmap_channels=heatmap_channels)
            for key, value in targets.items():
                targets[key] = value.to(accelerator.device, dtype=weight_dtype)

            # 计算损失 - 修复avg_factor计算方式，与原始Cycle-CenterNet项目保持一致
            # 基于热图中正样本数量而不是输入中心点数量
            avg_factor = max(1, targets['heatmap_target'].eq(1).sum().item())
            losses = criterion(predictions, targets, avg_factor=avg_factor)

            # 计算总损失：L_total = L_k + λ_off * L_off + L_p
            # 确保所有模型输出都参与损失计算，解决DDP未使用参数问题
            # 明确让所有预测分支都参与梯度计算
            # 修复：predictions[0] 是字典，需要对字典中的所有值求和
            pred_dict = predictions[0]
            pred_sum = sum(v.sum() for v in pred_dict.values())
            loss = losses['loss_heatmap'] + 0.0 * pred_sum

            # 强制计算所有分支的损失，即使权重为0也要参与计算
            if 'loss_offset' in losses:
                loss = loss + losses['loss_offset']

            # 添加配对损失（如果存在）
            if 'loss_pairing' in losses:
                loss = loss + losses['loss_pairing']

            # 添加其他损失（如果存在）
            if 'loss_c2v' in losses:
                loss = loss + losses['loss_c2v']

            if 'loss_v2c' in losses:
                loss = loss + losses['loss_v2c']
            total_loss += loss.item() * images.shape[0]
            total_samples += images.shape[0]
            test_pbar.update(1)

    test_pbar.close()
    avg_loss = total_loss / max(total_samples, 1)

    # 记录验证指标
    accelerator.log({"val_loss": avg_loss}, step=global_step)

    # 输出最终的综合指标
    msg = (f"\nTotal Metrics\n"
           f"AvgLoss: {avg_loss:.6f}")
    logger.info(msg)
    file_logger.info(msg)

    # 可视化功能集成
    if accelerator.is_main_process:
        try:
            from modules.visualization.table_structure_visualizer_ms import TableStructureVisualizerMS

            # 检查是否启用可视化
            vis_config = getattr(config, 'visualization', None)
            if vis_config is not None and getattr(vis_config, 'enabled', False):
                # 保存当前模型状态
                current_training_mode = model.training

                # 创建可视化器 (ModelScope 版本)
                visualizer = TableStructureVisualizerMS(
                    config=config,
                    device=accelerator.device,
                    weight_dtype=weight_dtype
                )

                # 使用unwrap_model避免分布式通信问题
                unwrapped_model = accelerator.unwrap_model(model)

                # 生成可视化结果
                visualizer.visualize_validation_samples(
                    model=unwrapped_model,
                    global_step=global_step,
                    accelerator=accelerator
                )

                # 恢复模型状态
                model.train(current_training_mode)

        except Exception as e:
            logger.warning(f"可视化过程出错，但不影响训练: {e}")
            # 确保模型状态正确
            if 'current_training_mode' in locals():
                model.train(current_training_mode)

    if config.ema.enabled and ema_handler is not None:
        ema_handler.restore(model)
    model.train()

    return avg_loss


def load_checkpoint_state(config, accelerator, logger, file_logger):
    """
    加载检查点状态

    Returns:
        tuple: (start_steps, start_epoch, ema_path, model_state_dict,
                optimizer_ckpt, lr_scheduler_ckpt, load_state_dict_msg)
    """
    start_steps = 0
    start_epoch = 0
    ema_path = None
    model_state_dict = None
    optimizer_ckpt = None
    lr_scheduler_ckpt = None
    load_state_dict_msg = None

    resume_from_checkpoint = config.checkpoint.resume.from_checkpoint
    latest_checkpoint_info = find_latest_checkpoint(
        config.basic.output_dir,
        accelerator,
        logger,
        file_logger
    )

    # 如果指定了checkpoint路径，优先使用指定的
    if resume_from_checkpoint is not None and os.path.exists(resume_from_checkpoint):
        resume_ckpt_dir = resume_from_checkpoint
        checkpoint_type = "specified"
    elif latest_checkpoint_info is not None:
        _, resume_ckpt_dir, checkpoint_type = latest_checkpoint_info
        resume_from_checkpoint = None  # 清空，表示使用自动发现的
    else:
        resume_ckpt_dir = None
        checkpoint_type = None

    if resume_ckpt_dir is not None:
        # 加载模型状态
        model_state_dict = torch.load(os.path.join(resume_ckpt_dir, "pytorch_model.bin"), map_location='cpu')

        # 加载EMA状态
        tmp_ema_path = os.path.join(resume_ckpt_dir, "pytorch_model_ema.bin")
        if config.ema.enabled and os.path.exists(tmp_ema_path):
            ema_path = tmp_ema_path

        # 加载训练状态
        training_state_path = os.path.join(resume_ckpt_dir, "training_state.bin")
        if os.path.exists(training_state_path):
            training_state = torch.load(training_state_path, map_location='cpu')
            start_steps = training_state["global_step"]
            start_epoch = training_state["epoch"]
            # 恢复随机状态
            if "random_states" in training_state:
                set_random_states(training_state["random_states"])
        else:
            raise FileNotFoundError(f"Training state file not found: {training_state_path}. "
                                    f"This checkpoint may be from an older version. Please retrain from scratch.")

        # 加载优化器和调度器状态
        optimizer_ckpt = os.path.join(resume_ckpt_dir, "optimizer.bin")
        lr_scheduler_ckpt = os.path.join(resume_ckpt_dir, "scheduler.bin")

        load_state_dict_msg = f"load state dict from {resume_ckpt_dir}"
        if accelerator.is_main_process:
            msg = f"Reusing {checkpoint_type} checkpoint from {resume_ckpt_dir}"
            logger.info(msg)
            file_logger.info(msg)

            # 记录恢复的训练状态
            msg = f"Restored training state: global_step={start_steps}, epoch={start_epoch}"
            logger.info(msg)
            file_logger.info(msg)

    return start_steps, start_epoch, ema_path, model_state_dict, optimizer_ckpt, lr_scheduler_ckpt, load_state_dict_msg


def create_model_and_ema(config, accelerator, model_state_dict, ema_path, weight_dtype, load_state_dict_msg):
    """
    创建模型和EMA处理器

    Returns:
        tuple: (model, ema_handler)
    """
    # 模型定义初始化 (ModelScope 版本)
    model = create_cycle_centernet_ms_model(
        config={
            'base_name': config.model.get('base_name', 'dla34'),
            'pretrained': config.model.get('pretrained', False),
            'down_ratio': config.model.get('down_ratio', 4),
            'head_conv': config.model.get('head_conv', 256),
            'checkpoint_path': config.model.get('checkpoint_path', None),
        }
    )
    # model = torch_utils.convert_batchnorm_to_apex_sync_batchnorm(model)
    model = torch_utils.convert_batchnorm_to_sync_batchnorm(model)
    if model_state_dict is not None:
        model.load_state_dict(model_state_dict, strict=True)
        logger.info(f"model: {load_state_dict_msg}")
        file_logger.info(f"model: {load_state_dict_msg}")
    model.to(accelerator.device, weight_dtype)
    model.train()

    ema_handler = None
    if config.ema.enabled:
        ema_handler = EMAHandler(
            model=model,
            decay=config.ema.decay,
            device=accelerator.device,
            weight_dtype=weight_dtype,
            start_step=config.ema.start_step,
            update_period=config.ema.update_period,
            log_updates=False,
        )
        if ema_path is not None:
            ema_handler.load(ema_path)

    return model, ema_handler


def setup_training_components(config, model, optimizer_ckpt, lr_scheduler_ckpt, accelerator):
    """
    设置训练组件（损失函数、优化器、调度器等）

    Returns:
        tuple: (loss_criterion, optimizer, lr_scheduler, max_train_steps)
    """
    # 创建损失函数 (ModelScope 版本)
    loss_criterion = create_cycle_centernet_ms_loss(
        heatmap_loss_weight=config.loss.weights.heatmap,
        reg_loss_weight=config.loss.weights.offset,
        c2v_loss_weight=config.loss.weights.center2vertex,
        v2c_loss_weight=config.loss.weights.vertex2center
    )
    loss_criterion = loss_criterion.to(device=accelerator.device)

    # 初始化优化策略
    params_to_opt = model.parameters()

    optimizer = get_optimizer(
        config=config,
        params_to_opt=params_to_opt,
        optimizer_ckpt=optimizer_ckpt,
    )

    # 准备数据集，训练步数、Epoch预估、accelerator.prepare 等调度
    num_split = accelerator.num_processes
    assert config.training.batch_size % num_split == 0, \
        (f"batch_size: {config.training.batch_size} needs to be divisible by num_processes={num_split}")

    if config.basic.seed is not None:
        seed = int(config.basic.seed)
    else:
        seed = random.randint(1, 100000)

    # 准备数据集
    train_batch_size_per_device = config.training.batch_size // num_split
    train_datasets, train_loaders = prepare_dataloaders(config, "train", train_batch_size_per_device, seed=seed)
    _, val_loaders = prepare_dataloaders(config, "val", train_batch_size_per_device, seed=seed)

    if not train_loaders:
        raise ValueError("No training data loaders available!")

    if not val_loaders:
        raise ValueError("No validation data loaders available!")

    train_dataloader_total_steps = sum(len(loader) for _, loader in train_loaders)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps)
    max_train_steps = config.training.epochs * num_update_steps_per_epoch

    lr_scheduler = get_scheduler(
        config.training.scheduler.type,
        optimizer=optimizer,
        num_training_steps=max_train_steps,
        power=config.training.scheduler.power,
        num_cycles=config.training.scheduler.cosine.num_cycles,
        num_warmup_steps=config.training.scheduler.warmup.steps * accelerator.num_processes,
    )
    if lr_scheduler_ckpt is not None:
        lr_scheduler_state_dict = torch.load(lr_scheduler_ckpt, map_location='cpu')
        lr_scheduler.load_state_dict(lr_scheduler_state_dict)

    return loss_criterion, optimizer, lr_scheduler, max_train_steps, train_datasets, train_loaders, val_loaders, seed


def handle_dry_run_mode(config, model, accelerator, weight_dtype, train_loaders):
    """
    处理干运行模式

    Returns:
        bool: 是否执行了干运行模式
    """
    # 检查是否启用干运行模式
    if hasattr(config, 'debug_visualization') and config.debug_visualization.dry_run:
        logger.info("启用干运行模式，开始数据可视化...")

        # 导入干运行可视化器
        from modules.visualization.dry_run_visualizer import DryRunVisualizer

        # 创建干运行可视化器
        dry_run_visualizer = DryRunVisualizer(config)

        # 设置模型为评估模式
        model.eval()

        # 执行干运行可视化
        dry_run_batches = config.debug_visualization.dry_run_batches
        logger.info(f"将可视化 {dry_run_batches} 个批次的数据")

        with torch.no_grad():
            for batch_idx, (flag, batch) in enumerate(walk_dataloaders(train_loaders)):
                if batch_idx >= dry_run_batches:
                    break

                logger.info(f"正在处理批次 {batch_idx + 1}/{dry_run_batches}")

                # 准备目标（复用训练时的目标生成逻辑）
                images = batch['images'].to(accelerator.device, dtype=weight_dtype)
                output_size = (config.data.processing.image_size[0] // 4,
                               config.data.processing.image_size[1] // 4)  # 假设下采样4倍
                heatmap_channels = getattr(config.data.processing, 'heatmap_channels', 2)  # 默认双通道
                targets = prepare_targets(batch, output_size, heatmap_channels=heatmap_channels)

                # 将目标移动到CPU进行可视化
                cpu_targets = {}
                for key, value in targets.items():
                    cpu_targets[key] = value.cpu()

                # 将图像移动到CPU
                cpu_batch = {}
                for key, value in batch.items():
                    if isinstance(value, torch.Tensor):
                        cpu_batch[key] = value.cpu()
                    else:
                        cpu_batch[key] = value

                # 执行可视化
                dry_run_visualizer.visualize_batch(cpu_batch, cpu_targets, batch_idx)

        logger.info("干运行模式完成，程序退出")
        return True

    return False


def handle_visualization_only_mode(config, model, accelerator, weight_dtype, start_steps):
    """
    处理只可视化模式

    Returns:
        bool: 是否执行了只可视化模式
    """
    # 检查是否只执行可视化
    if getattr(config.basic, 'only_vis_log', False):
        logger.info("启用只可视化模式，开始执行可视化...")

        if accelerator.is_main_process:
            try:
                from modules.visualization.table_structure_visualizer_ms import TableStructureVisualizerMS

                # 检查是否启用可视化
                vis_config = getattr(config, 'visualization', None)
                if vis_config is not None and getattr(vis_config, 'enabled', False):
                    # 保存当前模型状态
                    current_training_mode = model.training

                    # 创建可视化器 (ModelScope 版本)
                    visualizer = TableStructureVisualizerMS(
                        config=config,
                        device=accelerator.device,
                        weight_dtype=weight_dtype
                    )

                    # 使用unwrap_model避免分布式通信问题
                    unwrapped_model = accelerator.unwrap_model(model)

                    # 生成可视化结果
                    visualizer.visualize_validation_samples(
                        model=unwrapped_model,
                        global_step=start_steps,
                        accelerator=accelerator
                    )

                    # 恢复模型状态
                    model.train(current_training_mode)

                    logger.info("可视化完成")
                else:
                    logger.warning("可视化功能未启用，请检查配置文件中的 visualization.enabled 设置")

            except Exception as e:
                logger.error(f"可视化过程出错: {e}")
                # 确保模型状态正确
                if 'current_training_mode' in locals():
                    model.train(current_training_mode)

        logger.info("只可视化模式完成，程序退出")
        return True

    return False


def initialize_best_model_record(best_loss_model_record):
    """
    初始化最佳模型记录

    Returns:
        dict: 最佳模型记录数据
    """
    best_loss_record_data = {"avg_loss": float('inf')}
    if os.path.exists(best_loss_model_record):
        with open(best_loss_model_record, 'r', encoding='utf-8') as f:
            data = json.load(f)
            best_loss_record_data["avg_loss"] = data["avg_loss"]
    return best_loss_record_data


def prepare_accelerator_components(accelerator, model, optimizer, lr_scheduler, train_loaders, val_loaders):
    """
    准备accelerator组件

    Returns:
        tuple: (model, optimizer, lr_scheduler, train_loaders)
    """
    logger.info("Prepare everything with our accelerator\n")

    # 为了解决分布式训练中的未使用参数问题，使用monkey patch
    if accelerator.num_processes > 1:
        find_unused_parameters = True
        logger.info(f"分布式训练模式：应用DDP修复，设置find_unused_parameters={find_unused_parameters}")

        # 保存原始DDP类
        original_ddp = torch.nn.parallel.DistributedDataParallel

        # 创建修改后的DDP类
        class FixedDDP(original_ddp):
            def __init__(self, *args, **kwargs):
                kwargs['find_unused_parameters'] = find_unused_parameters
                super().__init__(*args, **kwargs)

        # 临时替换DDP类
        torch.nn.parallel.DistributedDataParallel = FixedDDP

        try:
            model, optimizer, lr_scheduler = accelerator.prepare(model, optimizer, lr_scheduler)
            logger.info(f"DDP修复已应用，find_unused_parameters={find_unused_parameters}")
        finally:
            # 恢复原始DDP类
            torch.nn.parallel.DistributedDataParallel = original_ddp
    else:
        model, optimizer, lr_scheduler = accelerator.prepare(model, optimizer, lr_scheduler)

    for i, loader in enumerate(train_loaders):
        flag, loader = loader
        loader = accelerator.prepare(loader)
        train_loaders[i] = (flag, loader)

    return model, optimizer, lr_scheduler, train_loaders, val_loaders


def calculate_final_training_steps(config, accelerator, train_loaders, max_train_steps, train_datasets, seed):
    """
    计算最终训练步数并记录训练信息

    Returns:
        int: 最终训练步数
    """
    # 经过prepare之后实际长度会发生变化
    train_dataloader_total_steps_after_prepare = sum(len(loader) for _, loader in train_loaders)
    num_update_steps_per_epoch_after_prepare = math.ceil(train_dataloader_total_steps_after_prepare)
    max_train_steps_after_prepare = config.training.epochs * num_update_steps_per_epoch_after_prepare

    # 使用更稳定的max_train_steps计算：取两次计算的最大值，避免因prepare导致的步数减少
    final_max_train_steps = max(max_train_steps, max_train_steps_after_prepare)

    # 记录步数变化信息
    if accelerator.is_main_process:
        train_batch_size_per_device = config.training.batch_size // accelerator.num_processes

        logger.info(f"训练步数计算:")
        logger.info(f"  prepare前: steps/epoch, 总计 {max_train_steps} steps")
        logger.info(f"  prepare后: {train_dataloader_total_steps_after_prepare} steps/epoch, 总计 {max_train_steps_after_prepare} steps")
        logger.info(f"  最终使用: {final_max_train_steps} steps")

        msg = "\n\n"
        msg += "***** Running training *****\n"
        msg += f"  Num checkpoints to keep: {config.checkpoint.save.keep_num}\n"
        msg += f"  Enable EMA model: {config.ema.enabled}\n"
        msg += f"  Dataset seed: {seed}\n"
        msg += f"  Num examples = {sum(len(dataset) for dataset in train_datasets)}\n"
        msg += f"  Num epochs = {config.training.epochs}\n"
        msg += f"  Batch size per device = {train_batch_size_per_device}\n"
        msg += f"  Total train batch size (w. parallel, distributed & accumulation) = {config.training.batch_size}\n"
        msg += f"  Total optimization steps = {final_max_train_steps}\n"
        msg += "***** Running training *****\n"
        logger.info(msg)
        file_logger.info(msg)

    return final_max_train_steps


def run_training_loop(
    config, model, accelerator, ema_handler, loss_criterion,
    optimizer, lr_scheduler, weight_dtype, train_loaders, val_loaders,
    global_step, first_epoch, max_train_steps, best_loss_model_record, best_loss_record_data
):
    """
    运行训练循环
    """
    # 初始化进度条
    progress_bar = tqdm(
        range(0, max_train_steps),
        initial=global_step,
        desc="Steps",
        disable=not accelerator.is_local_main_process,
    )

    # 训练循环
    for epoch in range(first_epoch, config.training.epochs):
        for step, (flag, batch) in enumerate(walk_dataloaders(train_loaders)):
            images = batch['images'].to(accelerator.device, dtype=weight_dtype)

            # 前向传播
            predictions = model(images)

            # 准备目标 - 修复：predictions[0] 是字典，需要从 'hm' 键获取热图张量
            heatmap_tensor = predictions[0]['hm']  # [B, 2, H, W]
            output_size = heatmap_tensor.shape[2:]  # (H, W)
            heatmap_channels = getattr(config.data.processing, 'heatmap_channels', 2)  # 默认双通道
            targets = prepare_targets(batch, output_size, heatmap_channels=heatmap_channels)
            for key, value in targets.items():
                targets[key] = value.to(accelerator.device, dtype=weight_dtype)

            # 计算损失 - 修复avg_factor计算方式，与原始Cycle-CenterNet项目保持一致
            # 基于热图中正样本数量而不是输入中心点数量
            avg_factor = max(1, targets['heatmap_target'].eq(1).sum().item())
            losses = loss_criterion(predictions, targets, avg_factor=avg_factor)

            # 计算总损失：L_total = L_k + λ_off * L_off + L_p
            # 确保所有模型输出都参与损失计算，解决DDP未使用参数问题
            # 明确让所有预测分支都参与梯度计算
            # 修复：predictions[0] 是字典，需要对字典中的所有值求和
            pred_dict = predictions[0]
            pred_sum = sum(v.sum() for v in pred_dict.values())
            loss = losses['loss_heatmap'] + 0.0 * pred_sum

            # 强制计算所有分支的损失，即使权重为0也要参与计算
            if 'loss_offset' in losses:
                loss = loss + losses['loss_offset']

            # 添加配对损失（如果存在）
            if 'loss_pairing' in losses:
                loss = loss + losses['loss_pairing']

            # 添加其他损失（如果存在）
            if 'loss_c2v' in losses:
                loss = loss + losses['loss_c2v']

            if 'loss_v2c' in losses:
                loss = loss + losses['loss_v2c']

            # 反向传播
            accelerator.backward(loss)
            avg_loss = accelerator.gather(loss.repeat(config.training.batch_size)).mean().item()

            # 梯度裁剪
            if config.training.gradient.clip_norm:
                params_to_opt = model.parameters()
                accelerator.clip_grad_norm_(params_to_opt, config.training.gradient.clip_value)

            # 优化器步骤
            optimizer.step()
            lr_scheduler.step()
            optimizer.zero_grad()

            if config.ema.enabled:
                ema_handler.update(accelerator.unwrap_model(model), global_step)

            # 更新进度条
            progress_bar.update(1)
            global_step += 1

            if accelerator.is_main_process:
                logs = {
                    # "dflag": flag,
                    "loss": loss.detach().item(),
                    "avg_loss": avg_loss,
                    "lr": lr_scheduler.get_last_lr()[0],
                }
                progress_bar.set_postfix(**logs)
                accelerator.log(logs, step=global_step)

            if accelerator.is_main_process and global_step % config.checkpoint.save.steps == 0:
                save_path = os.path.join(config.basic.output_dir, f"checkpoint-{global_step}")
                save_state(
                    save_path,
                    model,
                    optimizer,
                    lr_scheduler,
                    accelerator,
                    config.checkpoint.save.keep_num,
                    ema_handler=ema_handler,
                    global_step=global_step,
                    epoch=epoch,
                    random_states=get_random_states(),
                    logger=logger,
                    file_logger=file_logger,
                )

                # 下面是根据不同记录保存最佳模型
                val_metrics = log_validation(
                    config, model, ema_handler, global_step, accelerator, weight_dtype, val_loaders
                )
                save_best_checkpoints(
                    config,
                    accelerator,
                    model,
                    ema_handler,
                    optimizer,
                    lr_scheduler,
                    global_step,
                    val_metrics,
                    best_loss_model_record,
                    best_loss_record_data,
                )

            if global_step >= max_train_steps:
                break

        print()

        # 当前Epoch训练结束，保存当前模型
        if accelerator.is_main_process and config.checkpoint.save.every_n_epoch > 0 and \
                (epoch + 1) % config.checkpoint.save.every_n_epoch == 0:
            save_path = os.path.join(config.basic.output_dir, f"model_epoch-{epoch + 1}")
            save_state(
                save_path,
                model,
                optimizer,
                lr_scheduler,
                accelerator,
                ema_handler=ema_handler,
                global_step=global_step,
                epoch=epoch + 1,
                random_states=get_random_states(),
                logger=logger,
                file_logger=file_logger,
            )

    progress_bar.close()

    return global_step


def save_final_model(config, model, optimizer, lr_scheduler, accelerator, ema_handler, global_step):
    """
    保存最终模型
    """
    accelerator.wait_for_everyone()
    if accelerator.is_main_process:
        save_path = os.path.join(config.basic.output_dir, f"model_final")
        save_state(
            save_path,
            model,
            optimizer,
            lr_scheduler,
            accelerator,
            ema_handler=ema_handler,
            global_step=global_step,
            epoch=config.training.epochs,
            random_states=get_random_states(),
            logger=logger,
            file_logger=file_logger,
        )


def main():
    """主训练函数"""
    global config
    if config is None:
        config = parse_args()

    # 准备训练环境
    accelerator, weight_dtype = prepare_training_enviornment_v2(config, logger)

    # 初始化最佳模型记录
    best_loss_model_record = os.path.join(config.basic.output_dir, "best_loss_model", "record.json")
    os.makedirs(Path(best_loss_model_record).parent, exist_ok=True)

    # 加载检查点状态
    start_steps, start_epoch, ema_path, model_state_dict, optimizer_ckpt, lr_scheduler_ckpt, load_state_dict_msg = \
        load_checkpoint_state(config, accelerator, logger, file_logger)

    # 创建模型和EMA处理器
    model, ema_handler = create_model_and_ema(
        config, accelerator, model_state_dict, ema_path, weight_dtype, load_state_dict_msg
    )

    # 设置训练组件
    loss_criterion, optimizer, lr_scheduler, max_train_steps, train_datasets, train_loaders, val_loaders, seed = \
        setup_training_components(config, model, optimizer_ckpt, lr_scheduler_ckpt, accelerator)

    # 处理干运行模式
    if handle_dry_run_mode(config, model, accelerator, weight_dtype, train_loaders):
        return

    # 初始化最佳模型记录
    best_loss_record_data = initialize_best_model_record(best_loss_model_record)

    # 准备accelerator组件
    model, optimizer, lr_scheduler, train_loaders, val_loaders = prepare_accelerator_components(
        accelerator, model, optimizer, lr_scheduler, train_loaders, val_loaders
    )

    # 初始化训练跟踪器
    if accelerator.is_main_process:
        exp_date = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        accelerator.init_trackers(f"{config.distributed.tracker_project_name}-{exp_date}")

    # 处理只可视化模式
    if handle_visualization_only_mode(config, model, accelerator, weight_dtype, start_steps):
        accelerator.end_training()
        return

    # 计算最终训练步数并记录训练信息
    max_train_steps = calculate_final_training_steps(
        config, accelerator, train_loaders, max_train_steps, train_datasets, seed
    )

    # 设置起始epoch和global_step
    global_step = start_steps
    first_epoch = start_epoch

    # 运行训练循环
    global_step = run_training_loop(
        config, model, accelerator, ema_handler, loss_criterion,
        optimizer, lr_scheduler, weight_dtype, train_loaders, val_loaders,
        global_step, first_epoch, max_train_steps, best_loss_model_record, best_loss_record_data
    )

    # 训练结束，保存最终模型
    save_final_model(config, model, optimizer, lr_scheduler, accelerator, ema_handler, global_step)
    accelerator.end_training()


if __name__ == '__main__':
    main()
