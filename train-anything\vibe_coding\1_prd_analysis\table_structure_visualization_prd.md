# 表格结构识别训练可视化功能需求文档 (PRD)

**Time:** 2025-01-08  
**Author:** <EMAIL>  
**Version:** 1.0  

## 1. 功能概述

在表格结构识别训练过程中，为 `log_validation` 函数增加可视化功能，将模型的预测结果（表格结构）可视化在原始图片上，并保存可视化图片，用于监控模型训练效果和调试模型性能。

## 2. 技术背景

- **训练脚本：** `training_loops/table_structure_recognition/train_cycle_centernet.py`
- **配置文件：** `configs/table_structure_recognition/cycle_centernet/cycle_centernet_config.yaml`
- **可视化保存路径：** `assets/vis4tsr`
- **参考实现：** `training_loops/image_denoising/train_lama_rgb_denoise.py` 中的图片保存逻辑

## 3. 功能需求详细说明

### 3.1 可视化内容定义

#### 3.1.1 预测结果可视化
- **边界框绘制：** 在原图上叠加绘制预测的表格边界框
  - 颜色：绿色
  - 透明度：80%
  - 线条粗细：适中（2-3像素）

- **关键点绘制：** 在原图上绘制预测的关键点
  - 颜色：红色
  - 透明度：80%
  - 点的大小：适中（半径3-5像素）

- **中心点绘制：** 在原图上绘制预测的中心点
  - 颜色：红色
  - 透明度：80%
  - 点的大小：适中（半径3-5像素）

#### 3.1.2 热图可视化
- **热图展示：** 将预测的热图单独可视化，与原图并排显示
- **热图格式：** 使用伪彩色映射，便于观察热图分布

#### 3.1.3 最终输出格式
- **组合图片：** 每个样本生成一张包含以下内容的组合图片：
  - 左侧：原图 + 预测结果叠加（边界框、关键点、中心点）
  - 右侧：预测热图的伪彩色可视化

### 3.2 触发条件和频率

#### 3.2.1 触发时机
- **验证阶段：** 每次执行 `log_validation` 函数时自动触发
- **训练步数：** 按照现有的验证间隔（配置文件中的 `checkpoint.validation.steps`）

#### 3.2.2 样本选择策略
- **样本数量限制：** 每次验证只处理前10个样本
- **样本排序：** 对验证集中的样本文件名进行排序，确保每次选择的样本一致
- **样本来源：** 从验证数据加载器中获取样本

### 3.3 配置文件扩展

#### 3.3.1 新增配置项
在 `cycle_centernet_config.yaml` 中新增可视化配置节：

```yaml
# 可视化配置
visualization:
  # 是否启用可视化功能
  enabled: true
  # 可视化样本图片路径（用于纯图片可视化，不依赖验证集标注）
  sample_images_dir: "assets/vis4tsr"
  # 每次可视化的样本数量
  max_samples: 10
  # 可视化结果保存路径
  output_dir: "visualization_results"
```

### 3.4 图片保存组织方式

#### 3.4.1 目录结构
参考 `train_lama_rgb_denoise.py` 的保存逻辑：
```
{output_dir}/
├── visualization_results/
│   ├── latest/                    # 最新的可视化结果
│   └── running_steps/             # 按训练步数保存的历史结果
│       ├── step_100/
│       ├── step_200/
│       └── ...
```

#### 3.4.2 文件命名规则
- **拼接图片：** `visualization_step_{global_step}_part_{part_idx}.png`
- **分组策略：** 每10张图片拼接成一个大图（与Lama脚本保持一致）

### 3.5 可视化展示规范

#### 3.5.1 图片质量要求
- **保存格式：** PNG格式
- **图片质量：** 高清，无压缩损失
- **分辨率：** 保持原图分辨率，确保细节清晰

#### 3.5.2 视觉效果要求
- **无文字标注：** 不添加任何文字说明或图例
- **颜色对比：** 确保预测结果在原图上有良好的视觉对比度
- **布局清晰：** 原图与热图并排显示，比例协调

## 4. 技术实现要求

### 4.1 性能约束
- **训练速度影响：** 可视化过程应尽量减少对训练速度的影响
- **内存使用：** 合理控制可视化过程中的内存占用
- **异步处理：** 如有必要，可考虑异步保存图片

### 4.2 代码集成要求
- **模块化设计：** 可视化功能应作为独立模块，便于维护和扩展
- **配置驱动：** 通过配置文件控制可视化行为，支持开关控制
- **错误处理：** 可视化失败不应影响正常的训练流程

### 4.3 存储管理
- **不自动清理：** 不删除历史可视化结果，便于后续分析
- **目录管理：** 自动创建必要的目录结构
- **文件覆盖：** 同一步数的可视化结果可以覆盖

## 5. 验收标准

### 5.1 功能验收
- [ ] 可视化功能可通过配置文件开关控制
- [ ] 每次验证时自动生成可视化结果
- [ ] 预测结果正确叠加在原图上（边界框、关键点、中心点）
- [ ] 热图正确并排显示
- [ ] 图片按规定格式和目录结构保存

### 5.2 性能验收
- [ ] 可视化过程对训练速度影响小于5%
- [ ] 内存使用合理，无内存泄漏
- [ ] 生成的图片质量符合要求

### 5.3 集成验收
- [ ] 与现有训练流程无缝集成
- [ ] 配置文件扩展正确
- [ ] 错误处理机制完善

## 6. 依赖和约束

### 6.1 技术依赖
- **图像处理库：** PIL/Pillow, OpenCV, matplotlib
- **可视化库：** matplotlib, numpy
- **现有框架：** accelerate, torch

### 6.2 数据约束
- **输入数据：** 纯图片文件，无标注信息
- **模型输出：** Cycle-CenterNet模型的预测结果
- **文件格式：** 支持常见图片格式（jpg, png等）

## 7. 后续扩展考虑

### 7.1 功能扩展
- 支持更多可视化样式和颜色配置
- 支持不同模型架构的可视化适配
- 支持可视化结果的定量分析

### 7.2 性能优化
- 支持GPU加速的图像处理
- 支持批量并行可视化
- 支持可视化结果的压缩存储
